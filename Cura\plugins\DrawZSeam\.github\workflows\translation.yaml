name: "Autmatically update i18n files"

on:
  push:
    branches-ignore:
      - 'release'
      - '**.**'

jobs:
   update_translation:
     name: "Update plugin translation"
     runs-on: "ubuntu-latest"

     steps:
      - name: "Translation"
        id: translation
        uses: Elkin-Vasily/cura-plugin-translation@v1
        with:
          translation_folder: 'resources/i18n'
          translation_name: 'measuretool'
          plugin_name: 'Measure Tool'

      - if: ${{ steps.translation.outputs.template_updated || steps.translation.outputs.locales_updated }}
        name: 'Create Pull Request'
        uses: peter-evans/create-pull-request@v4
        with:
          branch: ${{github.ref_name}}-translation
          delete-branch: true
          title: '[CI] Translation update'
          body: 'Translation was automatically updated'
          commit-message: '[CI] Translation update'
          labels: 'translation'
