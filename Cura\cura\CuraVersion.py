# Copyright (c) 2023 UltiMaker
# Cura is released under the terms of the LGPLv3 or higher.

CuraAppName = "cura"
CuraAppDisplayName = "UltiMaker Cura"
CuraVersion = "5.10.0-alpha.0"
CuraBuildType = ""
CuraDebugMode = False
CuraCloudAPIRoot = "https://api.ultimaker.com"
CuraCloudAPIVersion = "1"
CuraCloudAccountAPIRoot = "https://account.ultimaker.com"
CuraMarketplaceRoot = "https://marketplace.ultimaker.com"
CuraDigitalFactoryURL = "https://digitalfactory.ultimaker.com"
CuraLatestURL = "https://software.ultimaker.com/latest.json"

ConanInstalls = {'uranium': {'version': '5.10.0', 'revision': None}, 'cura_resources': {'version': '5.10.0-alpha.0+428ea3', 'revision': 'eae1614a902ac22f2b785fcab219ab4a'}, 'cura_binary_data': {'version': '5.10.0-alpha.0+fcc178', 'revision': 'f20ba9a881700c8ccf9a4d8f310788f0'}, 'fdm_materials': {'version': '5.10.0-alpha.0+6a0aa0', 'revision': '93dcd61802806e73ae9102237b25f36b'}, 'dulcificum': {'version': '0.2.1', 'revision': '54697c569ce5889ab4955d4a32da5daf'}, 'pysavitar': {'version': '5.3.0', 'revision': '2527fe9a8d353cf599bf8102c4a60cbc7b3a0d1c'}, 'pynest2d': {'version': '5.3.0', 'revision': 'a44ffd40f4ad593091a9066589c0a1cf240275d5'}, 'cpython': {'version': '3.10.4', 'revision': 'dea9778d0b99f0b1b2133e7b2c15dca9'}, 'clipper': {'version': '6.4.2', 'revision': 'c1833d66de687b42c707231e7c5486bd'}, 'openssl': {'version': '3.2.0', 'revision': 'd52f6cbf28a54701393831929f47dbbd'}, 'protobuf': {'version': '3.21.12', 'revision': '2099e6c66fcd31b52c3afc0c096c1d02'}, 'boost': {'version': '1.82.0', 'revision': 'a379246d83d09536a3a1bc96c6ffc153'}, 'spdlog': {'version': '1.12.0', 'revision': 'baf01ff8c9be09bb6cb6c62c1804012c'}, 'fmt': {'version': '10.1.1', 'revision': 'bdeb86a2fc0fe23f1a77e3e1c316cb3d'}, 'zlib': {'version': '1.2.13', 'revision': '4e74ebf1361fe6fb60326f473f276eb5'}, 'pyarcus': {'version': '5.4.1', 'revision': 'ec8062aee5d3938ed77840130a375b1ac88d24c4'}, 'nlohmann_json': {'version': '3.11.2', 'revision': '1ded6ae5d200a68ac17c51d528b945e2'}, 'range-v3': {'version': '0.12.0', 'revision': '4c05d91d7b40e6b91b44b5345ac64408'}, 'ctre': {'version': '3.7.2', 'revision': '86bd3592feebcdafd2ab7b8a1aad0c80'}, 'pybind11': {'version': '2.10.4', 'revision': 'bfa96f955a3f5d975f2ba962c40000ff'}, 'savitar': {'version': '5.3.0', 'revision': 'e66e4e8f69e8764411d01b09140d04de8552552b'}, 'nest2d': {'version': '5.3.0', 'revision': '0e37a675ea212360daf2794541ad2c8b'}, 'expat': {'version': '2.4.1', 'revision': '742c681fc10b0ee1f11b490b483fa48e'}, 'libffi': {'version': '3.2.1', 'revision': '33b3eff20f1be5d569235ae100266729'}, 'mpdecimal': {'version': '2.5.0', 'revision': 'ef7f121607e10372c07a668830887b79'}, 'bzip2': {'version': '1.0.8', 'revision': 'd00dac990f08d991998d624be81a9526'}, 'sqlite3': {'version': '3.36.0', 'revision': '791ea1c6dc87facb455a40d2531aa088'}, 'tk': {'version': '8.6.10', 'revision': '1e8cbe0b5d8257de6bc6904da048766f'}, 'xz_utils': {'version': '5.2.5', 'revision': '3cf4a3a039d632b68119da6e434bfac6'}, 'arcus': {'version': '5.4.1', 'revision': 'ff973afb7564b1ec406fecbc98424376'}, 'pugixml': {'version': '1.12.1', 'revision': 'd0378a7e4e32d2d379c3a272269e4330'}, 'nlopt': {'version': '2.7.0', 'revision': '42f607e6bc9e04c81b75543d57511e8c'}, 'tcl': {'version': '8.6.10', 'revision': 'd694cc97f48640f0d66aa90b16cdc5cd'}}
PythonInstalls = {}