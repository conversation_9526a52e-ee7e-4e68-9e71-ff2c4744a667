# Cura
# Copyright (C) 2022 Ultimaker B.V.
# This file is distributed under the same license as the Cura package.
# <AUTHOR> <EMAIL>, 2022.
#
msgid ""
msgstr ""
"Project-Id-Version: Cura 5.1\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-11-06 10:43+0000\n"
"PO-Revision-Date: 2023-02-16 20:35+0100\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: DenyCZ <www.github.com/DenyCZ>\n"
"Language: cs_CZ\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 3.3.2\n"

msgctxt "prime_tower_mode description"
msgid "<html>How to generate the prime tower:<ul><li><b>Normal:</b> create a bucket in which secondary materials are primed</li><li><b>Interleaved:</b> create a prime tower as sparse as possible. This will save time and filament, but is only possible if the used materials adhere to each other</li></ul></html>"
msgstr ""

msgctxt "cool_during_extruder_switch description"
msgid "<html>Whether to activate the cooling fans during a nozzle switch. This can help reducing oozing by cooling the nozzle faster:<ul><li><b>Unchanged:</b> keep the fans as they were previously</li><li><b>Only last extruder:</b> turn on the fan of the last used extruder, but turn the others off (if any). This is useful if you have completely separate extruders.</li><li><b>All fans:</b> turn on all fans during nozzle switch. This is useful if you have a single cooling fan, or multiple fans that stay close to each other.</li></ul></html>"
msgstr ""

msgctxt "brim_inside_margin description"
msgid "A brim around a model may touch an other model where you don't want it. This removes all brim within this distance from brimless models."
msgstr ""

msgctxt "ironing_inset description"
msgid "A distance to keep from the edges of the model. Ironing all the way to the edge of the mesh may result in a jagged edge on your print."
msgstr "Vzdálenost, která se má držet od okrajů modelu. Žehlení až k okraji mřížky může vést k zubatému okraji na výtisku."

msgctxt "material_no_load_move_factor description"
msgid "A factor indicating how much the filament gets compressed between the feeder and the nozzle chamber, used to determine how far to move the material for a filament switch."
msgstr "Faktor udávající, jak moc se vlákno stlačí mezi podavačem a komorou trysky, používá se k určení, jak daleko se má pohybovat materiál pro spínač vlákna."

msgctxt "roofing_angles description"
msgid "A list of integer line directions to use when the top surface skin layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Seznam směrů celočíselných čar, které se použijí, když horní povrchové vrstvy používají čáry nebo vzor cik cak. Prvky ze seznamu se používají postupně jako vrstvy a jakmile je dosaženo konce seznamu, začíná znovu na začátku. Položky seznamu jsou odděleny čárkami a celý seznam je obsažen v hranatých závorkách. Výchozí je prázdný seznam, což znamená použití tradičních výchozích úhlů (45 a 135 stupňů)."

msgctxt "skin_angles description"
msgid "A list of integer line directions to use when the top/bottom layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Seznam směrů celočíselných čar, které se použijí, když horní / dolní vrstvy používají čáry nebo vzor zig zag. Prvky ze seznamu se používají postupně jako vrstvy a jakmile je dosaženo konce seznamu, začíná znovu na začátku. Položky seznamu jsou odděleny čárkami a celý seznam je obsažen v hranatých závorkách. Výchozí je prázdný seznam, což znamená použití tradičních výchozích úhlů (45 a 135 stupňů)."

msgctxt "support_infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angle 0 degrees."
msgstr "Seznam směrů celého čísla, které je třeba použít. Prvky ze seznamu se používají postupně jako vrstvy a jakmile je dosaženo konce seznamu, začíná znovu na začátku. Položky seznamu jsou odděleny čárkami a celý seznam je obsažen v hranatých závorkách. Výchozí je prázdný seznam, což znamená použít výchozí úhel 0 stupňů."

msgctxt "support_bottom_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Seznam směrů celého čísla, které je třeba použít. Prvky ze seznamu se používají postupně jako vrstvy a jakmile je dosaženo konce seznamu, začíná znovu na začátku. Položky seznamu jsou odděleny čárkami a celý seznam je obsažen v hranatých závorkách. Výchozí je prázdný seznam, což znamená použít výchozí úhly (střídavě mezi 45 a 135 stupni, pokud jsou rozhraní poměrně silná nebo 90 stupňů)."

msgctxt "support_interface_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Seznam směrů celého čísla, které je třeba použít. Prvky ze seznamu se používají postupně jako vrstvy a jakmile je dosaženo konce seznamu, začíná znovu na začátku. Položky seznamu jsou odděleny čárkami a celý seznam je obsažen v hranatých závorkách. Výchozí je prázdný seznam, což znamená použít výchozí úhly (střídavě mezi 45 a 135 stupni, pokud jsou rozhraní poměrně silná nebo 90 stupňů)."

msgctxt "support_roof_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Seznam směrů celého čísla, které je třeba použít. Prvky ze seznamu se používají postupně jako vrstvy a jakmile je dosaženo konce seznamu, začíná znovu na začátku. Položky seznamu jsou odděleny čárkami a celý seznam je obsažen v hranatých závorkách. Výchozí je prázdný seznam, což znamená použít výchozí úhly (střídavě mezi 45 a 135 stupni, pokud jsou rozhraní poměrně silná nebo 90 stupňů)."

msgctxt "infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees for the lines and zig zag patterns and 45 degrees for all other patterns)."
msgstr "Seznam směrů celého čísla, které je třeba použít. Prvky ze seznamu se používají postupně jako vrstvy a jakmile je dosaženo konce seznamu, začíná znovu na začátku. Položky seznamu jsou odděleny čárkami a celý seznam je obsažen v hranatých závorkách. Výchozí je prázdný seznam, který znamená použití tradičních výchozích úhlů (45 a 135 stupňů pro čáry a cik-cak vzory a 45 stupňů pro všechny ostatní vzory)."

msgctxt "nozzle_disallowed_areas description"
msgid "A list of polygons with areas the nozzle is not allowed to enter."
msgstr "Seznam polygonů s oblastmi, do kterých nesmí vstoupit tryska."

msgctxt "machine_disallowed_areas description"
msgid "A list of polygons with areas the print head is not allowed to enter."
msgstr "Seznam polygonů s oblastmi, do kterých tisková hlava nemá přístup."

msgctxt "support_tree_branch_reach_limit description"
msgid "A recomendation to how far branches can move from the points they support. Branches can violate this value to reach their destination (buildplate or a flat part of the model). Lowering this value will make the support more sturdy, but increase the amount of branches (and because of that material usage/print time) "
msgstr "Doporučení, jak daleko se mohou větve pohnout od bodu, který podporují. Větve mohou tuto hodnotu porušit, aby dosáhly svého cíle (podložka nebo plochá část modelu). Snížení této hodnoty učiní podporu více pevnou, ale zvýší počet větví (a společně s tím také množství použitého materiálu a dobu tisku) "

msgctxt "extruder_prime_pos_abs label"
msgid "Absolute Extruder Prime Position"
msgstr "Absolutní výchozí pozice extruderu"

msgctxt "adaptive_layer_height_variation label"
msgid "Adaptive Layers Maximum Variation"
msgstr "Maximální variabilita adaptivních vrstev"

msgctxt "adaptive_layer_height_threshold label"
msgid "Adaptive Layers Topography Size"
msgstr "Velikost topografie adaptivních vrstev"

msgctxt "adaptive_layer_height_variation_step label"
msgid "Adaptive Layers Variation Step Size"
msgstr "Velikost kroku adaptivní vrstvy"

msgctxt "adaptive_layer_height_enabled description"
msgid "Adaptive layers computes the layer heights depending on the shape of the model."
msgstr "Adaptivní vrstvy vypočítávají výšky vrstev v závislosti na tvaru modelu."

msgctxt "extra_infill_lines_to_support_skins description"
msgid "Add extra lines into the infill pattern to support skins above.   This option prevents holes or plastic blobs that sometime show in complex shaped skins due to the infill below not correctly supporting the skin layer being printed above.  'Walls' supports just the outlines of the skin, whereas 'Walls and Lines' also supports the ends of the lines that make up the skin."
msgstr ""

msgctxt "infill_wall_line_count description"
msgid ""
"Add extra walls around the infill area. Such walls can make top/bottom skin lines sag down less which means you need less top/bottom skin layers for the same quality at the cost of some extra material.\n"
"This feature can combine with the Connect Infill Polygons to connect all the infill into a single extrusion path without the need for travels or retractions if configured right."
msgstr ""
"Okolo výplně přidejte další zdi. Takové stěny mohou snížit horní a dolní linii povrchu, což znamená, že potřebujete méně vrchních / spodních vrstev povrchu pro stejnou kvalitu za cenu nějakého dalšího materiálu.\n"
"Tato funkce se může kombinovat s polygony Spojení výplně a spojit veškerou výplň do jediné cesty vytlačování bez nutnosti cest a stáhnutí, pokud je nakonfigurováno správně."

msgctxt "platform_adhesion description"
msgid "Adhesion"
msgstr "Adheze"

msgctxt "material_adhesion_tendency label"
msgid "Adhesion Tendency"
msgstr "Tendence adheze"

msgctxt "skin_overlap description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines, as a percentage of the line widths of the skin lines and the innermost wall. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any percentage over 50% may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "Upravte míru překrytí mezi stěnami a (koncovými body) osami povrchu jako procento šířky linií pokožky a nejvnitřnější stěny. Mírné překrytí umožňuje, aby se stěny pevně spojily s povrchem. Uvědomte si, že při stejné šířce linie povrchu a stěny může jakékoli procento nad 50% již způsobit, že jakýkoli povrch projde kolem zdi, protože v tomto bodě může pozice trysky extruderu povrchu už dosáhnout kolem středu zeď."

msgctxt "skin_overlap_mm description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any value over half the width of the wall may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "Upravte míru překrytí mezi stěnami a (koncovými body) osami porvchu. Mírné překrytí umožňuje, aby se stěny pevně spojily s povrchem. Je třeba si uvědomit, že při stejné šířce linie povrchu a stěny může jakákoli hodnota přesahující polovinu šířky stěny již způsobit, že jakýkoli povrch projde kolem zdi, protože v tomto bodě může pozice trysky extruderu povrchu již dosáhnout. kolem středu zdi."

msgctxt "infill_sparse_density description"
msgid "Adjusts the density of infill of the print."
msgstr "Upravuje hustotu výplně tisku."

msgctxt "support_interface_density description"
msgid "Adjusts the density of the roofs and floors of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Upravuje hustotu střech a podlah nosné konstrukce. Vyšší hodnota má za následek lepší přesahy, ale podpory je těžší odstranit."

msgctxt "support_tree_top_rate description"
msgid "Adjusts the density of the support structure used to generate the tips of the branches. A higher value results in better overhangs, but the supports are harder to remove. Use Support Roof for very high values or ensure support density is similarly high at the top."
msgstr "Upravuje hustotu struktury podpory použité pro generování konečků větví. Vyšší hodnota zajistí lepší převisy, ale bude těžší podpory odstranit. Použijte střechu podpory pro vysoké hodnoty, anebo se ujistěte, že hustota podpor nahoře je podobně vysoká."

msgctxt "support_infill_rate description"
msgid "Adjusts the density of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Nastavuje hustotu podpůrné struktury. Vyšší hodnota má za následek lepší přesahy, ale podpory je těžší odstranit."

msgctxt "material_diameter description"
msgid "Adjusts the diameter of the filament used. Match this value with the diameter of the used filament."
msgstr "Nastavuje průměr použitého vlákna filamentu. Srovnejte tuto hodnotu s průměrem použitého vlákna."

msgctxt "support_type description"
msgid "Adjusts the placement of the support structures. The placement can be set to touching build plate or everywhere. When set to everywhere the support structures will also be printed on the model."
msgstr "Upravuje umístění podpůrných struktur. Umístění lze nastavit tak, aby se dotýkalo podložky nebo kdekoli. Pokud je nastavena všude, podpůrné struktury budou také vytištěny na modelu."

msgctxt "prime_tower_wipe_enabled description"
msgid "After printing the prime tower with one nozzle, wipe the oozed material from the other nozzle off on the prime tower."
msgstr "Po vytištění hlavní věže jednou tryskou otřete vyteklý materiál z druhé trysky na hlavní věži."

msgctxt "retraction_hop_after_extruder_switch description"
msgid "After the machine switched from one extruder to the other, the build plate is lowered to create clearance between the nozzle and the print. This prevents the nozzle from leaving oozed material on the outside of a print."
msgstr "Poté, co se stroj přepnul z jednoho extrudéru na druhý, sestavovací deska se spustí, aby se vytvořila vůle mezi tryskou a tiskem. Tím se zabrání tomu, aby tryska zanechávala vyteklý materiál na vnější straně tisku."

msgctxt "retraction_combing option all"
msgid "All"
msgstr "Vše"

msgctxt "print_sequence option all_at_once"
msgid "All at Once"
msgstr "Vše najednou"

msgctxt "cool_during_extruder_switch option all_fans"
msgid "All fans"
msgstr ""

msgctxt "resolution description"
msgid "All settings that influence the resolution of the print. These settings have a large impact on the quality (and print time)"
msgstr "Všechna nastavení, která ovlivňují rozlišení tisku. Tato nastavení mají velký vliv na kvalitu (a dobu tisku)"

msgctxt "user_defined_print_order_enabled description"
msgid "Allows you to order the object list to manually set the print sequence. First object from the list will be printed first."
msgstr ""

msgctxt "alternate_extra_perimeter label"
msgid "Alternate Extra Wall"
msgstr "Alternativní zeď navíc"

msgctxt "alternate_carve_order label"
msgid "Alternate Mesh Removal"
msgstr "Alternativní odstranění sítí"

msgctxt "material_alternate_walls label"
msgid "Alternate Wall Directions"
msgstr "Střídat směr zdí"

msgctxt "material_alternate_walls description"
msgid "Alternate wall directions every other layer and inset. Useful for materials that can build up stress, like for metal printing."
msgstr "Střídat směr zdí s každou vrstvou. Je užitečné pro materiály, ve kterých se může hromadit napětí, jako například kovové materiály."

msgctxt "machine_buildplate_type option aluminum"
msgid "Aluminum"
msgstr "Hliník"

msgctxt "machine_always_write_active_tool label"
msgid "Always Write Active Tool"
msgstr "Vždy Zapisovat Aktivní Nástroj"

msgctxt "travel_retract_before_outer_wall description"
msgid "Always retract when moving to start an outer wall."
msgstr "Vždy zatáhnout filament, když se přesouvá k začátku vnější zdi."

msgctxt "xy_offset description"
msgid "Amount of offset applied to all polygons in each layer. Positive values can compensate for too big holes; negative values can compensate for too small holes."
msgstr "Množství ofsetu aplikovaného na všechny polygony v každé vrstvě. Pozitivní hodnoty mohou kompenzovat příliš velké díry; záporné hodnoty mohou kompenzovat příliš malé díry."

msgctxt "xy_offset_layer_0 description"
msgid "Amount of offset applied to all polygons in the first layer. A negative value can compensate for squishing of the first layer known as \"elephant's foot\"."
msgstr "Množství ofsetu aplikovaného na všechny polygony v první vrstvě. Záporná hodnota může kompenzovat pískání první vrstvy známé jako „sloní noha“."

msgctxt "support_offset description"
msgid "Amount of offset applied to all support polygons in each layer. Positive values can smooth out the support areas and result in more sturdy support."
msgstr "Množství ofsetu aplikovaného na všechny podpůrné polygony v každé vrstvě. Pozitivní hodnoty mohou vyhladit oblasti podpory a vést k robustnější podpoře."

msgctxt "support_bottom_offset description"
msgid "Amount of offset applied to the floors of the support."
msgstr "Částka kompenzace použitá na podlahy podpory."

msgctxt "support_roof_offset description"
msgid "Amount of offset applied to the roofs of the support."
msgstr "Množství offsetu aplikovaný na střechy podpěry."

msgctxt "support_interface_offset description"
msgid "Amount of offset applied to the support interface polygons."
msgstr "Množství offsetu aplikovaného na polygony rozhraní podpory."

msgctxt "wipe_retraction_amount description"
msgid "Amount to retract the filament so it does not ooze during the wipe sequence."
msgstr "Délka pro zasunutí filamentu tak, aby během sekvence stírání nevytekla."

msgctxt "sub_div_rad_add description"
msgid "An addition to the radius from the center of each cube to check for the boundary of the model, as to decide whether this cube should be subdivided. Larger values lead to a thicker shell of small cubes near the boundary of the model."
msgstr "Přídavek k poloměru od středu každé krychle ke kontrole hranice modelu, aby se rozhodlo, zda má být tato krychle rozdělena. Větší hodnoty vedou k silnější skořápce malých kostek poblíž hranice modelu."

msgctxt "anti_overhang_mesh label"
msgid "Anti Overhang Mesh"
msgstr "Síť proti převisu"

msgctxt "material_anti_ooze_retracted_position label"
msgid "Anti-ooze Retracted Position"
msgstr "Pozice zabraňující úniku"

msgctxt "material_anti_ooze_retraction_speed label"
msgid "Anti-ooze Retraction Speed"
msgstr "Rychlost návratu při vytékání"

msgctxt "machine_use_extruder_offset_to_offset_coords description"
msgid "Apply the extruder offset to the coordinate system. Affects all extruders."
msgstr "Použít offset extruderu v souřadnicovém systému. Ovlivňuje všechny extrudery."

msgctxt "interlocking_enable description"
msgid "At the locations where models touch, generate an interlocking beam structure. This improves the adhesion between models, especially models printed in different materials."
msgstr "V místech, kde se modely dotýkají, budou generovány vzájemně propletené struktury. Ty zlepšují adhezi mezi modely, obzvláště u modelů tištěných z různých materiálů."

msgctxt "travel_avoid_other_parts label"
msgid "Avoid Printed Parts When Traveling"
msgstr "Při cestování se vyhněte tištěným součástem"

msgctxt "travel_avoid_supports label"
msgid "Avoid Supports When Traveling"
msgstr "Při pohybu se vyhnout podporám"

msgctxt "z_seam_position option back"
msgid "Back"
msgstr "Zpět"

msgctxt "z_seam_position option backleft"
msgid "Back Left"
msgstr "Zadní levá"

msgctxt "z_seam_position option backright"
msgid "Back Right"
msgstr "Zadní pravá"

msgctxt "machine_gcode_flavor option BFB"
msgid "Bits from Bytes"
msgstr "Bits from Bytes"

msgctxt "magic_mesh_surface_mode option both"
msgid "Both"
msgstr "Obojí"

msgctxt "support_interface_priority option nothing"
msgid "Both overlap"
msgstr "Překrýt obojí"

msgctxt "bottom_layers label"
msgid "Bottom Layers"
msgstr "Spodní vrstvy"

msgctxt "top_bottom_pattern_0 label"
msgid "Bottom Pattern Initial Layer"
msgstr "Vzor spodní počáteční vrstvy"

msgctxt "bottom_skin_expand_distance label"
msgid "Bottom Skin Expand Distance"
msgstr "Dolní vzdálenost rozšíření povrchu"

msgctxt "bottom_skin_preshrink label"
msgid "Bottom Skin Removal Width"
msgstr "Dolní šířka odstranění povrchu"

msgctxt "bottom_thickness label"
msgid "Bottom Thickness"
msgstr "Spodní tloušťka"

msgctxt "support_tree_top_rate label"
msgid "Branch Density"
msgstr "Hustota větví"

msgctxt "support_tree_branch_diameter label"
msgid "Branch Diameter"
msgstr "Průměr větve"

msgctxt "support_tree_branch_diameter_angle label"
msgid "Branch Diameter Angle"
msgstr "Úhel průměru větve"

msgctxt "material_break_preparation_retracted_position label"
msgid "Break Preparation Retracted Position"
msgstr "Maximální napnutí filamentu při zahřátí"

msgctxt "material_break_preparation_speed label"
msgid "Break Preparation Retraction Speed"
msgstr "Maximální rychlost napnutí při zahřátí"

msgctxt "material_break_preparation_temperature label"
msgid "Break Preparation Temperature"
msgstr "Teplota přípravy na napnutí"

msgctxt "material_break_retracted_position label"
msgid "Break Retracted Position"
msgstr "Pozice napnutí"

msgctxt "material_break_speed label"
msgid "Break Retraction Speed"
msgstr "Rychlost navíjení vlákna"

msgctxt "material_break_temperature label"
msgid "Break Temperature"
msgstr "Teplota přerušení"

msgctxt "support_skip_some_zags label"
msgid "Break Up Support In Chunks"
msgstr "Rozdělte podporu na kousky"

msgctxt "bridge_fan_speed label"
msgid "Bridge Fan Speed"
msgstr "Rychlost ventilátoru při tisknutí můstku"

msgctxt "bridge_enable_more_layers label"
msgid "Bridge Has Multiple Layers"
msgstr "Můstek má více vrstev"

msgctxt "bridge_skin_density_2 label"
msgid "Bridge Second Skin Density"
msgstr "Hustota druhé vrstvy povrchu můstku"

msgctxt "bridge_fan_speed_2 label"
msgid "Bridge Second Skin Fan Speed"
msgstr "Rychlost ventilátoru při tisku druhé vrstvy povrchu můstku"

msgctxt "bridge_skin_material_flow_2 label"
msgid "Bridge Second Skin Flow"
msgstr "Průtok při tisku druhé vrstvy povrchu můstku"

msgctxt "bridge_skin_speed_2 label"
msgid "Bridge Second Skin Speed"
msgstr "Rychlost tisku druhé vrstvy povrchu můstku"

msgctxt "bridge_skin_density label"
msgid "Bridge Skin Density"
msgstr "Hustota povrchu mostu"

msgctxt "bridge_skin_material_flow label"
msgid "Bridge Skin Flow"
msgstr "Průtok při tisku povrchu můstku"

msgctxt "bridge_skin_speed label"
msgid "Bridge Skin Speed"
msgstr "Rychlost při tisku povrchu můstku"

msgctxt "bridge_skin_support_threshold label"
msgid "Bridge Skin Support Threshold"
msgstr "Prahová hodnota podpory povrchu mostu"

msgctxt "bridge_sparse_infill_max_density label"
msgid "Bridge Sparse Infill Max Density"
msgstr "Maximální hustota výplně mostu"

msgctxt "bridge_skin_density_3 label"
msgid "Bridge Third Skin Density"
msgstr "Hustota třetí vrstvy povrchu můstku"

msgctxt "bridge_fan_speed_3 label"
msgid "Bridge Third Skin Fan Speed"
msgstr "Rychlost ventilátoru při tisku třetí vrstvy povrchu můstku"

msgctxt "bridge_skin_material_flow_3 label"
msgid "Bridge Third Skin Flow"
msgstr "Průtok při tisku třetí vrstvy povrchu můstku"

msgctxt "bridge_skin_speed_3 label"
msgid "Bridge Third Skin Speed"
msgstr "Rychlost tisku třetí vrstvy povrchu můstku"

msgctxt "bridge_wall_coast label"
msgid "Bridge Wall Coasting"
msgstr "Coasting zdi můstku"

msgctxt "bridge_wall_material_flow label"
msgid "Bridge Wall Flow"
msgstr "Průtok při tisku zdi můstku"

msgctxt "bridge_wall_speed label"
msgid "Bridge Wall Speed"
msgstr "Rychlost tisku zdi můstku"

msgctxt "adhesion_type option brim"
msgid "Brim"
msgstr "Límec"

msgctxt "brim_inside_margin label"
msgid "Brim Avoid Margin"
msgstr ""

msgctxt "brim_gap label"
msgid "Brim Distance"
msgstr "Vzdálenost límce"

msgctxt "brim_line_count label"
msgid "Brim Line Count"
msgstr "Počet linek pro límec"

msgctxt "brim_location label"
msgid "Brim Location"
msgstr ""

msgctxt "brim_replaces_support label"
msgid "Brim Replaces Support"
msgstr "Límec nahrazuje podpory"

msgctxt "brim_width label"
msgid "Brim Width"
msgstr "Šířka límce"

msgctxt "build_fan_full_at_height label"
msgid "Build Fan Speed at Height"
msgstr ""

msgctxt "build_fan_full_layer label"
msgid "Build Fan Speed at Layer"
msgstr ""

msgctxt "platform_adhesion label"
msgid "Build Plate Adhesion"
msgstr "Adheze topné podložky"

msgctxt "adhesion_extruder_nr label"
msgid "Build Plate Adhesion Extruder"
msgstr "Extruder pro adhezi podložky"

msgctxt "adhesion_type label"
msgid "Build Plate Adhesion Type"
msgstr "Typ přilnavosti podložky"

msgctxt "machine_buildplate_type label"
msgid "Build Plate Material"
msgstr "Materiál podložky"

msgctxt "machine_shape label"
msgid "Build Plate Shape"
msgstr "Tvar podložky"

msgctxt "material_bed_temperature label"
msgid "Build Plate Temperature"
msgstr "Teplota podložky"

msgctxt "material_bed_temperature_layer_0 label"
msgid "Build Plate Temperature Initial Layer"
msgstr "Teplota podložky při počáteční vrstvě"

msgctxt "build_volume_temperature label"
msgid "Build Volume Temperature"
msgstr "Teplota sestavení"

msgctxt "bv_temp_anomaly_limit label"
msgid "Build Volume temperature Limit"
msgstr ""

msgctxt "bv_temp_warn_limit label"
msgid "Build Volume temperature Warning"
msgstr ""

msgctxt "build_volume_fan_nr label"
msgid "Build volume fan number"
msgstr ""

msgctxt "prime_tower_brim_enable description"
msgid "By enabling this setting, your prime-tower will get a brim, even if the model doesn't. If you want a sturdier base for a high tower, you can increase the base height."
msgstr ""

msgctxt "center_object label"
msgid "Center Object"
msgstr "Centrovat objekt"

msgctxt "conical_overhang_enabled description"
msgid "Change the geometry of the printed model such that minimal support is required. Steep overhangs will become shallow overhangs. Overhanging areas will drop down to become more vertical."
msgstr "Změňte geometrii tištěného modelu tak, aby byla vyžadována minimální podpora. Strmé převisy se stanou mělkými převisy. Převislé oblasti budou klesat dolů, aby se staly vertikálními."

msgctxt "support_structure description"
msgid "Chooses between the techniques available to generate support. \"Normal\" support creates a support structure directly below the overhanging parts and drops those areas straight down. \"Tree\" support creates branches towards the overhanging areas that support the model on the tips of those branches, and allows the branches to crawl around the model to support it from the build plate as much as possible."
msgstr "Volí mezi dostupnými technikami pro generování podpory. „Normální“ podpora vytváří podpůrnou strukturu přímo pod převislými částmi a upouští tyto oblasti přímo dolů. „Stromová“ podpora vytváří větve směrem k převislým oblastem, které podporují model na koncích těchto větví, a umožňuje větvím procházet se kolem modelu a podporovat je co nejvíce z konstrukční desky."

msgctxt "coasting_speed label"
msgid "Coasting Speed"
msgstr "Rychlost coastingu"

msgctxt "coasting_volume label"
msgid "Coasting Volume"
msgstr "Objem coastingu"

msgctxt "coasting_enable description"
msgid "Coasting replaces the last part of an extrusion path with a travel path. The oozed material is used to print the last piece of the extrusion path in order to reduce stringing."
msgstr "Coasting nahradí poslední část vytlačovací cesty cestou. Vyteklý materiál se používá k tisku posledního kusu vytlačovací cesty, aby se snížilo strunování."

msgctxt "retraction_combing label"
msgid "Combing Mode"
msgstr "Režím Combing"

msgctxt "retraction_combing description"
msgid "Combing keeps the nozzle within already printed areas when traveling. This results in slightly longer travel moves but reduces the need for retractions. If combing is off, the material will retract and the nozzle moves in a straight line to the next point. It is also possible to avoid combing over top/bottom skin areas or to only comb within the infill."
msgstr "Combing udržuje trysku v již vytištěných oblastech při cestování. To má za následek mírně delší pohybové tahy, ale snižuje potřebu zatažení. Pokud je combing vyplý, materiál se stáhne a tryska se pohybuje v přímce k dalšímu bodu. Je také možné vyhnout se combingu na horních / dolních částech povrchu nebo jen combing uvnitř výplně."

msgctxt "command_line_settings label"
msgid "Command Line Settings"
msgstr "Nastavení příkazové řádky"

msgctxt "infill_pattern option concentric"
msgid "Concentric"
msgstr "Soustředný"

msgctxt "ironing_pattern option concentric"
msgid "Concentric"
msgstr "Soustředný"

msgctxt "roofing_pattern option concentric"
msgid "Concentric"
msgstr "Soustředný"

msgctxt "support_bottom_pattern option concentric"
msgid "Concentric"
msgstr "Soustředný"

msgctxt "support_interface_pattern option concentric"
msgid "Concentric"
msgstr "Soustředný"

msgctxt "support_pattern option concentric"
msgid "Concentric"
msgstr "Soustředný"

msgctxt "support_roof_pattern option concentric"
msgid "Concentric"
msgstr "Soustředný"

msgctxt "top_bottom_pattern option concentric"
msgid "Concentric"
msgstr "Soustředný"

msgctxt "top_bottom_pattern_0 option concentric"
msgid "Concentric"
msgstr "Soustředný"

msgctxt "support_conical_angle label"
msgid "Conical Support Angle"
msgstr "Kónický podpěrný úhel"

msgctxt "support_conical_min_width label"
msgid "Conical Support Minimum Width"
msgstr "Minimální šířka kónické podpory"

msgctxt "zig_zaggify_infill label"
msgid "Connect Infill Lines"
msgstr "Spojovat čáry výplně"

msgctxt "connect_infill_polygons label"
msgid "Connect Infill Polygons"
msgstr "Připojte výplňové polygony"

msgctxt "zig_zaggify_support label"
msgid "Connect Support Lines"
msgstr "Spojovat linky podpor"

msgctxt "support_connect_zigzags label"
msgid "Connect Support ZigZags"
msgstr "Spojovat cig-cag podpory"

msgctxt "connect_skin_polygons label"
msgid "Connect Top/Bottom Polygons"
msgstr "Připojte horní / dolní polygony"

msgctxt "connect_infill_polygons description"
msgid "Connect infill paths where they run next to each other. For infill patterns which consist of several closed polygons, enabling this setting greatly reduces the travel time."
msgstr "Připojte výplňové cesty tam, kde běží vedle sebe. Pro výplňové vzory, které se skládají z několika uzavřených polygonů, umožňuje toto nastavení výrazně zkrátit dobu cestování."

msgctxt "support_connect_zigzags description"
msgid "Connect the ZigZags. This will increase the strength of the zig zag support structure."
msgstr "Připojte ZigZagy. Tím se zvýší pevnost nosné struktury klikatá zag."

msgctxt "zig_zaggify_support description"
msgid "Connect the ends of the support lines together. Enabling this setting can make your support more sturdy and reduce underextrusion, but it will cost more material."
msgstr "Spojte konce podpůrných linek dohromady. Aktivace tohoto nastavení může zvýšit vaši podporu a snížit podextruzi, ale bude to stát více materiálu."

msgctxt "zig_zaggify_infill description"
msgid "Connect the ends where the infill pattern meets the inner wall using a line which follows the shape of the inner wall. Enabling this setting can make the infill adhere to the walls better and reduce the effects of infill on the quality of vertical surfaces. Disabling this setting reduces the amount of material used."
msgstr "Konce, kde se vzor výplně setkává s vnitřní stěnou, pomocí čáry, která sleduje tvar vnitřní stěny. Aktivace tohoto nastavení může zlepšit přilnavost výplně ke stěnám a snížit vliv výplně na kvalitu svislých povrchů. Vypnutím tohoto nastavení se sníží množství použitého materiálu."

msgctxt "connect_skin_polygons description"
msgid "Connect top/bottom skin paths where they run next to each other. For the concentric pattern enabling this setting greatly reduces the travel time, but because the connections can happen midway over infill this feature can reduce the top surface quality."
msgstr "Propojte horní / dolní povrchové cesty tam, kde běží vedle sebe. Pro soustředné uspořádání umožňující toto nastavení výrazně zkracuje dobu cestování, ale protože se spojení může uskutečnit uprostřed výplně, může tato funkce snížit kvalitu povrchu."

msgctxt "z_seam_corner description"
msgid "Control whether corners on the model outline influence the position of the seam. None means that corners have no influence on the seam position. Hide Seam makes the seam more likely to occur on an inside corner. Expose Seam makes the seam more likely to occur on an outside corner. Hide or Expose Seam makes the seam more likely to occur at an inside or outside corner. Smart Hiding allows both inside and outside corners, but chooses inside corners more frequently, if appropriate."
msgstr "Určete, zda rohy na obrysu modelu ovlivňují polohu švu. Žádné znamená, že rohy nemají žádný vliv na polohu švu. Funkce \"Schovat šev\" zvyšuje pravděpodobnost, že se šev vyskytuje ve vnitřním rohu. \"Ukázat šev\" zvyšuje pravděpodobnost, že se šev objeví na vnějším rohu. \"Skrýt nebo vystavit šev\" zvyšuje pravděpodobnost, že šev nastane ve vnitřním nebo vnějším rohu. \"Inteligentní skrytí\" umožňuje vnitřní i vnější rohy, ale v případě potřeby vybírá vnitřní rohy častěji."

msgctxt "infill_multiplier description"
msgid "Convert each infill line to this many lines. The extra lines do not cross over each other, but avoid each other. This makes the infill stiffer, but increases print time and material usage."
msgstr "Převeďte každou výplňovou linii na tuto řadu řádků. Další čáry se nepřekrývají, ale vzájemně se vyhýbají. Díky tomu je výplň tužší, ale zvyšuje se doba tisku a spotřeba materiálu."

msgctxt "machine_nozzle_cool_down_speed label"
msgid "Cool Down Speed"
msgstr "Rychlost chlazení"

msgctxt "cooling description"
msgid "Cooling"
msgstr "Chlazení"

msgctxt "cooling label"
msgid "Cooling"
msgstr "Chlazení"

msgctxt "cool_during_extruder_switch label"
msgid "Cooling during extruder switch"
msgstr ""

msgctxt "infill_pattern option cross"
msgid "Cross"
msgstr "Křížek"

msgctxt "support_pattern option cross"
msgid "Cross"
msgstr "Křížek"

msgctxt "infill_pattern option cross_3d"
msgid "Cross 3D"
msgstr "3D Křížek"

msgctxt "cross_infill_pocket_size label"
msgid "Cross 3D Pocket Size"
msgstr "Velikost kapsy u 3D kříže"

msgctxt "cross_support_density_image label"
msgid "Cross Fill Density Image for Support"
msgstr "Obrázek s křížovou výplní pro podporu"

msgctxt "cross_infill_density_image label"
msgid "Cross Infill Density Image"
msgstr "Obrázek s křížovou výplní"

msgctxt "material_crystallinity label"
msgid "Crystalline Material"
msgstr "Krystalický materiál"

msgctxt "infill_pattern option cubic"
msgid "Cubic"
msgstr "Krychle"

msgctxt "infill_pattern option cubicsubdiv"
msgid "Cubic Subdivision"
msgstr "Kubické členění"

msgctxt "sub_div_rad_add label"
msgid "Cubic Subdivision Shell"
msgstr "Shell kubické rozdělení"

msgctxt "cutting_mesh label"
msgid "Cutting Mesh"
msgstr "Síť pro řezání"

msgctxt "material_flow_temp_graph description"
msgid "Data linking material flow (in mm3 per second) to temperature (degrees Celsius)."
msgstr "Data spojující tok materiálu (v mm3 za sekundu) s teplotou (ve stupních Celsia)."

msgctxt "machine_acceleration label"
msgid "Default Acceleration"
msgstr "Výchozí akcelerace"

msgctxt "default_material_bed_temperature label"
msgid "Default Build Plate Temperature"
msgstr "Výchozí teplota podložky"

msgctxt "machine_max_jerk_e label"
msgid "Default Filament Jerk"
msgstr "Výchozí jerk filamentu"

msgctxt "default_material_print_temperature label"
msgid "Default Printing Temperature"
msgstr "Výchozí teplota tisknutí"

msgctxt "machine_max_jerk_xy label"
msgid "Default X-Y Jerk"
msgstr "Výchozí X-Y jerk rychlost motoru"

msgctxt "machine_max_jerk_z label"
msgid "Default Z Jerk"
msgstr "Výchozí Z jerk rychlost motoru"

msgctxt "machine_max_jerk_xy description"
msgid "Default jerk for movement in the horizontal plane."
msgstr "Výchozí trhnutí pro pohyb ve vodorovné rovině."

msgctxt "machine_max_jerk_z description"
msgid "Default jerk for the motor of the Z-direction."
msgstr "Výchozí trhnutí pro motor ve směru Z."

msgctxt "machine_max_jerk_e description"
msgid "Default jerk for the motor of the filament."
msgstr "Výchozí trhnutí pro motor filamentu."

msgctxt "bridge_settings_enabled description"
msgid "Detect bridges and modify print speed, flow and fan settings while bridges are printed."
msgstr "Zjistěte mosty a upravte rychlost tisku, průtok a nastavení ventilátoru během tisku mostů."

msgctxt "scarf_split_distance description"
msgid "Determines the length of each step in the flow change when extruding along the scarf seam. A smaller distance will result in a more precise but also more complex G-code."
msgstr ""

msgctxt "scarf_joint_seam_length description"
msgid "Determines the length of the scarf seam, a seam type that should make the Z seam less visible. Must be higher than 0 to be effective."
msgstr ""

msgctxt "inset_direction description"
msgid "Determines the order in which walls are printed. Printing outer walls earlier helps with dimensional accuracy, as faults from inner walls cannot propagate to the outside. However printing them later allows them to stack better when overhangs are printed. When there is an uneven amount of total innner walls, the 'center last line' is always printed last."
msgstr "Určuje pořadí, v jakém budou tištěny zdi. Tištění vnějších zdí jako prvních pomáhá rozměrové přesnosti, jelikož se vady z vnitřních stěn nemohou přenášet na vnějšek. Naproti tomu tištění vnějších zdí nakonec dovoluje jejich lepší vrstvení při tištění převisů. Pokud je nepravidelný počet čar ve vnitřních stěnách, tisknou se 'zbylé střední čáry' nakonec."

msgctxt "infill_mesh_order description"
msgid "Determines the priority of this mesh when considering multiple overlapping infill meshes. Areas where multiple infill meshes overlap will take on the settings of the mesh with the highest rank. An infill mesh with a higher rank will modify the infill of infill meshes with lower rank and normal meshes."
msgstr "Určuje prioritu této sítě, když se překrývá více sítí výplně. U oblastí, kde se překrývá více sítí výplně, se nastavení přebírá ze sítě s nejvyšším pořadím. Síť výplně s vyšším pořadím bude modifikovat výplň sítě výplně s nižším pořadím a běžné sítě."

msgctxt "lightning_infill_support_angle description"
msgid "Determines when a lightning infill layer has to support anything above it. Measured in the angle given the thickness of a layer."
msgstr "Určuje, kdy má vrstva bleskové výplně nad sebou něco, co má podporovat. Zadává se jako úhel a řídí se tloušťkou vrstvy."

msgctxt "lightning_infill_overhang_angle description"
msgid "Determines when a lightning infill layer has to support the model above it. Measured in the angle given the thickness."
msgstr "Určuje, od jakého úhlu převisu bude vrstva bleskové výplň podporovat model nad sebou."

msgctxt "material_diameter label"
msgid "Diameter"
msgstr "Průměr"

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model label"
msgid "Diameter Increase To Model"
msgstr "Zvýšení průměru k modelu"

msgctxt "support_tree_bp_diameter description"
msgid "Diameter every branch tries to achieve when reaching the buildplate. Improves bed adhesion."
msgstr "Průměr, kterého se každá větev snaží dosáhnout, když se dotýká podložky. Zlepšuje přilnavost."

msgctxt "adhesion_type description"
msgid "Different options that help to improve both priming your extrusion and adhesion to the build plate. Brim adds a single layer flat area around the base of your model to prevent warping. Raft adds a thick grid with a roof below the model. Skirt is a line printed around the model, but not connected to the model."
msgstr "Různé možnosti, které pomáhají zlepšit jak vytlačování, tak adhezi k sestavovací desce. Límec přidává jedinou vrstvu roviny kolem základny vašeho modelu, aby se zabránilo deformaci. Raft přidává tlustou mřížku se střechou pod modelem. Okraj je čára vytištěná kolem modelu, ale není k modelu připojena."

msgctxt "machine_disallowed_areas label"
msgid "Disallowed Areas"
msgstr "Zakázané zóny"

msgctxt "infill_line_distance description"
msgid "Distance between the printed infill lines. This setting is calculated by the infill density and the infill line width."
msgstr "Vzdálenost mezi tištěnými výplněmi. Toto nastavení se vypočítá podle hustoty výplně a šířky výplně."

msgctxt "support_initial_layer_line_distance description"
msgid "Distance between the printed initial layer support structure lines. This setting is calculated by the support density."
msgstr "Vzdálenost mezi tištěnými liniemi základní struktury podpory. Toto nastavení se vypočítá podle hustoty podpory."

msgctxt "support_bottom_line_distance description"
msgid "Distance between the printed support floor lines. This setting is calculated by the Support Floor Density, but can be adjusted separately."
msgstr "Vzdálenost mezi tištěnými podpůrnými podlahovými liniemi. Toto nastavení se vypočítá podle hustoty podpůrné podlahy, ale lze ji upravit samostatně."

msgctxt "support_roof_line_distance description"
msgid "Distance between the printed support roof lines. This setting is calculated by the Support Roof Density, but can be adjusted separately."
msgstr "Vzdálenost mezi tištěnými oporami střechy. Toto nastavení se vypočítá podle hustoty nosné střechy, ale lze ji upravit samostatně."

msgctxt "support_line_distance description"
msgid "Distance between the printed support structure lines. This setting is calculated by the support density."
msgstr "Vzdálenost mezi tištěnými liniemi podpůrné struktury. Toto nastavení se vypočítá podle hustoty podpory."

msgctxt "support_bottom_distance description"
msgid "Distance from the print to the bottom of the support. Note that this is rounded up to the next layer height."
msgstr ""

msgctxt "support_top_distance description"
msgid "Distance from the top of the support to the print."
msgstr "Vzdálenost od horní strany podpory k tisku."

msgctxt "support_z_distance description"
msgid "Distance from the top/bottom of the support structure to the print. This gap provides clearance to remove the supports after the model is printed. The topmost support layer below the model might be a fraction of regular layers."
msgstr ""

msgctxt "infill_wipe_dist description"
msgid "Distance of a travel move inserted after every infill line, to make the infill stick to the walls better. This option is similar to infill overlap, but without extrusion and only on one end of the infill line."
msgstr "Vzdálenost pohybového pohybu vloženého za každou výplňovou linii, aby se výplň lepila ke stěnám lépe. Tato možnost je podobná překrývání výplně, ale bez vytlačování a pouze na jednom konci výplňové linky."

msgctxt "wall_0_wipe_dist description"
msgid "Distance of a travel move inserted after the outer wall, to hide the Z seam better."
msgstr "Vzdálenost pohybového posunu vloženého za vnější stěnu, aby se skryla Z šev lépe."

msgctxt "draft_shield_dist description"
msgid "Distance of the draft shield from the print, in the X/Y directions."
msgstr "Vzdálenost štítu před tiskem ve směru X / Y."

msgctxt "ooze_shield_dist description"
msgid "Distance of the ooze shield from the print, in the X/Y directions."
msgstr "Vzdálenost štítu od tisku ve směru X / Y."

msgctxt "support_xy_distance_overhang description"
msgid "Distance of the support structure from the overhang in the X/Y directions."
msgstr "Vzdálenost podpor od převisu ve směru X/Y."

msgctxt "support_xy_distance description"
msgid "Distance of the support structure from the print in the X/Y directions."
msgstr "Vzdálenost podpůrné struktury od tisku ve směru X / Y."

msgctxt "meshfix_fluid_motion_shift_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr ""

msgctxt "meshfix_fluid_motion_small_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr ""

msgctxt "min_infill_area description"
msgid "Don't generate areas of infill smaller than this (use skin instead)."
msgstr "Nevytvářejte oblasti výplně menší než tato (místo toho použijte povrch)."

msgctxt "draft_shield_height label"
msgid "Draft Shield Height"
msgstr "Výška štítu modelu"

msgctxt "draft_shield_height_limitation label"
msgid "Draft Shield Limitation"
msgstr "Limitace štítu modelu"

msgctxt "draft_shield_dist label"
msgid "Draft Shield X/Y Distance"
msgstr "X/Y vzdálenost štítu modelu"

msgctxt "support_mesh_drop_down label"
msgid "Drop Down Support Mesh"
msgstr "Podpory pod celou sítí"

msgctxt "dual label"
msgid "Dual Extrusion"
msgstr "Dvojitá extruze"

msgctxt "gradual_flow_discretisation_step_size description"
msgid "Duration of each step in the gradual flow change"
msgstr "Doba trvání každého kroku v postupné změně průtoku"

msgctxt "machine_shape option elliptic"
msgid "Elliptic"
msgstr "Eliptická"

msgctxt "acceleration_enabled label"
msgid "Enable Acceleration Control"
msgstr "Povolit ovládání akcelerace"

msgctxt "bridge_settings_enabled label"
msgid "Enable Bridge Settings"
msgstr "Povolit nastavení mostu"

msgctxt "coasting_enable label"
msgid "Enable Coasting"
msgstr "Povolit Coasting"

msgctxt "support_conical_enabled label"
msgid "Enable Conical Support"
msgstr "Povolit kuželovou podporu"

msgctxt "draft_shield_enabled label"
msgid "Enable Draft Shield"
msgstr "Zapnout štít modelu"

msgctxt "meshfix_fluid_motion_enabled label"
msgid "Enable Fluid Motion"
msgstr ""

msgctxt "ironing_enabled label"
msgid "Enable Ironing"
msgstr "Povolit žehlení"

msgctxt "jerk_enabled label"
msgid "Enable Jerk Control"
msgstr "Povolit kontrolu trhu"

msgctxt "machine_nozzle_temp_enabled label"
msgid "Enable Nozzle Temperature Control"
msgstr "Povolit řízení teploty trysek"

msgctxt "ooze_shield_enabled label"
msgid "Enable Ooze Shield"
msgstr "Povolit Ooze štít"

msgctxt "prime_blob_enable label"
msgid "Enable Prime Blob"
msgstr "Povolit primární blob"

msgctxt "prime_tower_enable label"
msgid "Enable Prime Tower"
msgstr "Povolit hlavní věže"

msgctxt "cool_fan_enabled label"
msgid "Enable Print Cooling"
msgstr "Povolit chlazení při tisku"

msgctxt "ppr_enable label"
msgid "Enable Print Process Reporting"
msgstr ""

msgctxt "retraction_enable label"
msgid "Enable Retraction"
msgstr "Povolit retrakci"

msgctxt "support_brim_enable label"
msgid "Enable Support Brim"
msgstr "Povolit límec podpory"

msgctxt "support_bottom_enable label"
msgid "Enable Support Floor"
msgstr "Povolit podpory podlah"

msgctxt "support_interface_enable label"
msgid "Enable Support Interface"
msgstr "Povolit rozhraní podpor"

msgctxt "support_roof_enable label"
msgid "Enable Support Roof"
msgstr "Povolit střechu podpory"

msgctxt "acceleration_travel_enabled label"
msgid "Enable Travel Acceleration"
msgstr "Změnit akceleraci cestování"

msgctxt "jerk_travel_enabled label"
msgid "Enable Travel Jerk"
msgstr "Změnit okamžitou rychlost cestování"

msgctxt "ooze_shield_enabled description"
msgid "Enable exterior ooze shield. This will create a shell around the model which is likely to wipe a second nozzle if it's at the same height as the first nozzle."
msgstr "Povolit vnější ochranu proti úniku. Tím se vytvoří model kolem modelu, který pravděpodobně otře druhou trysku, pokud je ve stejné výšce jako první tryska."

msgctxt "gradual_flow_enabled description"
msgid "Enable gradual flow changes. When enabled, the flow is gradually increased/decreased to the target flow. This is useful for printers with a bowden tube where the flow is not immediately changed when the extruder motor starts/stops."
msgstr "Povolit postupné změny průtoku. Když je povoleno, průtok se postupně zvyšuje / snižuje na cílový průtok. Toto je užitečné pro tiskárny s Bowdenovou trubicí, kde se průtok okamžitě nezmění, když se spustí / zastaví extrudér."

msgctxt "ppr_enable description"
msgid "Enable print process reporting for setting threshold values for possible fault detection."
msgstr ""

msgctxt "small_skin_on_surface description"
msgid "Enable small (up to 'Small Top/Bottom Width') regions on the topmost skinned layer (exposed to air) to be filled with walls instead of the default pattern."
msgstr ""

msgctxt "jerk_enabled description"
msgid "Enables adjusting the jerk of print head when the velocity in the X or Y axis changes. Increasing the jerk can reduce printing time at the cost of print quality."
msgstr "Umožňuje nastavení trhnutí tiskové hlavy, když se mění rychlost v ose X nebo Y. Zvýšení trhnutí může zkrátit dobu tisku za cenu kvality tisku."

msgctxt "acceleration_enabled description"
msgid "Enables adjusting the print head acceleration. Increasing the accelerations can reduce printing time at the cost of print quality."
msgstr "Umožňuje nastavení zrychlení tiskové hlavy. Zvýšení zrychlení může zkrátit dobu tisku za cenu kvality tisku."

msgctxt "cool_fan_enabled description"
msgid "Enables the print cooling fans while printing. The fans improve print quality on layers with short layer times and bridging / overhangs."
msgstr "Aktivuje ventilátory chlazení tisku během tisku. Ventilátory zlepšují kvalitu tisku na vrstvách s krátkými časy a přemostěním / přesahy."

msgctxt "machine_end_gcode label"
msgid "End G-code"
msgstr "Ukončující G kód"

msgctxt "material_end_of_filament_purge_length label"
msgid "End of Filament Purge Length"
msgstr "Délka proplachu na konci vlákna"

msgctxt "material_end_of_filament_purge_speed label"
msgid "End of Filament Purge Speed"
msgstr "Rychlost proplachování na konci filamentu"

msgctxt "brim_replaces_support description"
msgid "Enforce brim to be printed around the model even if that space would otherwise be occupied by support. This replaces some regions of the first layer of support by brim regions."
msgstr "Vynutí vytištění límce kolem modelu, i když by tento prostor byl jinak obsazen podporou. To nahradí některé regiony první vrstvy podpory okrajovými regiony."

msgctxt "brim_location option everywhere"
msgid "Everywhere"
msgstr ""

msgctxt "support_type option everywhere"
msgid "Everywhere"
msgstr "Všude"

msgctxt "slicing_tolerance option exclusive"
msgid "Exclusive"
msgstr "Exkluzivní"

msgctxt "experimental label"
msgid "Experimental"
msgstr "Experimentální"

msgctxt "z_seam_corner option z_seam_corner_outer"
msgid "Expose Seam"
msgstr "Ukázat šev"

msgctxt "meshfix_extensive_stitching label"
msgid "Extensive Stitching"
msgstr "Rozsáhlé prošívání"

msgctxt "meshfix_extensive_stitching description"
msgid "Extensive stitching tries to stitch up open holes in the mesh by closing the hole with touching polygons. This option can introduce a lot of processing time."
msgstr "Rozsáhlé sešívání se snaží spojit otevřené otvory v mřížce uzavřením díry dotykem mnohoúhelníků. Tato možnost může přinést spoustu času zpracování."

msgctxt "extra_infill_lines_to_support_skins label"
msgid "Extra Infill Lines To Support Skins"
msgstr ""

msgctxt "infill_wall_line_count label"
msgid "Extra Infill Wall Count"
msgstr "Počet navíc výplní zdí"

msgctxt "skin_outline_count label"
msgid "Extra Skin Wall Count"
msgstr "Počet povrchových zdí navíc"

msgctxt "switch_extruder_extra_prime_amount description"
msgid "Extra material to prime after nozzle switching."
msgstr "Materiál navíc pro extruzi po změně trysky."

msgctxt "variant_name"
msgid "Extruder"
msgstr ""

msgctxt "extruder_prime_pos_x label"
msgid "Extruder Prime X Position"
msgstr "Primární pozice extruderu X"

msgctxt "extruder_prime_pos_y label"
msgid "Extruder Prime Y Position"
msgstr "Primární pozice extruderu Y"

msgctxt "extruder_prime_pos_z label"
msgid "Extruder Prime Z Position"
msgstr "První Z pozice extruderu"

msgctxt "machine_extruders_share_heater label"
msgid "Extruders Share Heater"
msgstr "Extrudery sdílí ohřívač"

msgctxt "machine_extruders_share_nozzle label"
msgid "Extruders Share Nozzle"
msgstr "Extrudery sdílí trysku"

msgctxt "material_extrusion_cool_down_speed label"
msgid "Extrusion Cool Down Speed Modifier"
msgstr "Modifikátor rychlosti chlazení extruze"

msgctxt "speed_equalize_flow_width_factor description"
msgid "Extrusion width based correction factor on the speed. At 0% the movement speed is kept constant at the Print Speed. At 100% the movement speed is adjusted so that the flow (in mm³/s) is kept constant, i.e. lines half the normal Line Width are printed twice as fast and lines twice as wide are printed half as fast. A value larger than 100% can help to compensate for the higher pressure required to extrude wide lines."
msgstr "Faktor korekce rychlosti v závislosti na šířce extruze. Při 0 % je rychlost pohybu konstantně na hodnotě Rychlost tisku. Při 100 % je rychlost pohybu upravena tak, aby byl tok (v mm³/s) konstantní. To znamená, že čáry o šířce poloviny nastavení Šířka čáry jsou tištěny dvojnásobnou rychlostí a čáry dvojnásobné šířky jsou tištěny poloviční rychlostí. Hodnota vyšší než 100 % může pomoci kompenzovat vysoký tlak potřebný k extruzi širokých čar."

msgctxt "cool_fan_speed label"
msgid "Fan Speed"
msgstr "Rychlost větráku"

msgctxt "support_fan_enable label"
msgid "Fan Speed Override"
msgstr "Přepsání rychlosti ventilátoru"

msgctxt "small_feature_max_length description"
msgid "Feature outlines that are shorter than this length will be printed using Small Feature Speed."
msgstr "Obrysy částí, které jsou kratší než tato délka, budou vytištěny pomocí funkce Rychlost malých částí."

msgctxt "experimental description"
msgid "Features that haven't completely been fleshed out yet."
msgstr "Nové vychytávky, které ještě nejsou venku."

msgctxt "machine_feeder_wheel_diameter label"
msgid "Feeder Wheel Diameter"
msgstr "Průměr kolečka feederu"

msgctxt "material_final_print_temperature label"
msgid "Final Printing Temperature"
msgstr "Konečná teplota tisku"

msgctxt "machine_firmware_retract label"
msgid "Firmware Retraction"
msgstr "Retrakce firmwaru"

msgctxt "support_extruder_nr_layer_0 label"
msgid "First Layer Support Extruder"
msgstr "Extruder pro první vrstvu"

msgctxt "material_flow label"
msgid "Flow"
msgstr "Průtok"

msgctxt "speed_equalize_flow_width_factor label"
msgid "Flow Equalization Ratio"
msgstr "Míra vyrovnávání toku"

msgctxt "flow_anomaly_limit label"
msgid "Flow Limit"
msgstr ""

msgctxt "flow_rate_extrusion_offset_factor label"
msgid "Flow Rate Compensation Factor"
msgstr "Faktor kompenzace průtoku"

msgctxt "flow_rate_max_extrusion_offset label"
msgid "Flow Rate Compensation Max Extrusion Offset"
msgstr "Kompenzace průtoku maximální posunutí extruze"

msgctxt "material_flow_temp_graph label"
msgid "Flow Temperature Graph"
msgstr "Graf teploty toku"

msgctxt "flow_warn_limit label"
msgid "Flow Warning"
msgstr ""

msgctxt "material_flow_layer_0 description"
msgid "Flow compensation for the first layer: the amount of material extruded on the initial layer is multiplied by this value."
msgstr "Kompenzace toku pro první vrstvu: množství materiálu vytlačovaného na počáteční vrstvě se vynásobí touto hodnotou."

msgctxt "skin_material_flow_layer_0 description"
msgid "Flow compensation on bottom lines of the first layer"
msgstr "Kompenzace toku pro čáry spodku modelu v první vrstvě"

msgctxt "infill_material_flow description"
msgid "Flow compensation on infill lines."
msgstr "Kompenzace toku na výplňových vedeních."

msgctxt "support_interface_material_flow description"
msgid "Flow compensation on lines of support roof or floor."
msgstr "Kompenzace toku na liniích podpůrné střechy nebo podlahy."

msgctxt "roofing_material_flow description"
msgid "Flow compensation on lines of the areas at the top of the print."
msgstr "Kompenzace toku na řádcích oblastí v horní části tisku."

msgctxt "prime_tower_flow description"
msgid "Flow compensation on prime tower lines."
msgstr "Kompenzace toku na hlavních liniích věží."

msgctxt "skirt_brim_material_flow description"
msgid "Flow compensation on skirt or brim lines."
msgstr "Kompenzace toku na okrajových nebo límcových liniích."

msgctxt "support_bottom_material_flow description"
msgid "Flow compensation on support floor lines."
msgstr "Kompenzace toku na podpůrných podlahových linkách."

msgctxt "support_roof_material_flow description"
msgid "Flow compensation on support roof lines."
msgstr "Kompenzace toku na podpůrných liniích střechy."

msgctxt "support_material_flow description"
msgid "Flow compensation on support structure lines."
msgstr "Kompenzace toku na podpůrných strukturách."

msgctxt "wall_0_material_flow_layer_0 description"
msgid "Flow compensation on the outermost wall line of the first layer."
msgstr "Kompenzace toku v nejvíce venkovní čáře stěny první vrstvy."

msgctxt "wall_0_material_flow description"
msgid "Flow compensation on the outermost wall line."
msgstr "Kompenzace průtoku na vnější linii stěny."

msgctxt "wall_0_material_flow_roofing description"
msgid "Flow compensation on the top surface outermost wall line."
msgstr "Kompensace toku na nejvíce vnější stěnové lince horní plochy."

msgctxt "wall_x_material_flow_roofing description"
msgid "Flow compensation on top surface wall lines for all wall lines except the outermost one."
msgstr "Kompensace toku na horní stěnové linky pro všechny stěnové linky kromě nejvnější."

msgctxt "skin_material_flow description"
msgid "Flow compensation on top/bottom lines."
msgstr "Kompenzace průtoku na horních / dolních řádcích."

msgctxt "wall_x_material_flow_layer_0 description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one, but only for the first layer"
msgstr "Kompenzace toku pro všechny čáry stěn kromě té nejvíce venkovní, ale pouze pro první vrstvu"

msgctxt "wall_x_material_flow description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one."
msgstr "Kompenzace toku na liniích stěn pro všechny linie stěn s výjimkou těch nejvíce venkovních."

msgctxt "wall_material_flow description"
msgid "Flow compensation on wall lines."
msgstr "Kompenzace průtoku na stěnách."

msgctxt "material_flow description"
msgid "Flow compensation: the amount of material extruded is multiplied by this value."
msgstr "Kompenzace toku: množství vytlačovaného materiálu se vynásobí touto hodnotou."

msgctxt "meshfix_fluid_motion_angle label"
msgid "Fluid Motion Angle"
msgstr ""

msgctxt "meshfix_fluid_motion_shift_distance label"
msgid "Fluid Motion Shift Distance"
msgstr ""

msgctxt "meshfix_fluid_motion_small_distance label"
msgid "Fluid Motion Small Distance"
msgstr ""

msgctxt "material_flush_purge_length label"
msgid "Flush Purge Length"
msgstr "Délka proplachování"

msgctxt "material_flush_purge_speed label"
msgid "Flush Purge Speed"
msgstr "Rychlost proplachování"

msgctxt "reset_flow_duration description"
msgid "For any travel move longer than this value, the material flow is reset to the paths target flow"
msgstr "Pro jakýkoli pohyb delší než tato hodnota se materiálový průtok resetuje na cílový průtok dráhy"

msgctxt "min_wall_line_width description"
msgid "For thin structures around once or twice the nozzle size, the line widths need to be altered to adhere to the thickness of the model. This setting controls the minimum line width allowed for the walls. The minimum line widths inherently also determine the maximum line widths, since we transition from N to N+1 walls at some geometry thickness where the N walls are wide and the N+1 walls are narrow. The widest possible wall line is twice the Minimum Wall Line Width."
msgstr "Pro struktury o šířce okolo jedno až dvojnásobku velikosti trysky musí být šířky čar upravovány, aby to se dodržovala správná tloušťka modelu. Toto nastavení ovládá minimální dovolenou šířku čáry pro zdi. Z minimální šířky čáry se také odvozuje maximální šířka, jelikož při určité tloušťce tvaru se přechází z N na N + 1 zdí, kdy je N zdí širokých, zatímco N + 1 zdi jsou úzké. Nejvyšší možná šířka čáry zdi je tedy dvojnásobek tohoto nastavení."

msgctxt "z_seam_position option front"
msgid "Front"
msgstr "Přední"

msgctxt "z_seam_position option frontleft"
msgid "Front Left"
msgstr "Přední levá"

msgctxt "z_seam_position option frontright"
msgid "Front Right"
msgstr "Přední pravá"

msgctxt "draft_shield_height_limitation option full"
msgid "Full"
msgstr "Plná"

msgctxt "magic_fuzzy_skin_enabled label"
msgid "Fuzzy Skin"
msgstr "Rozmazaný povrch"

msgctxt "magic_fuzzy_skin_point_density label"
msgid "Fuzzy Skin Density"
msgstr "Hustota nejasného povrchu"

msgctxt "magic_fuzzy_skin_outside_only label"
msgid "Fuzzy Skin Outside Only"
msgstr "Rozmazaný povrch pouze venku"

msgctxt "magic_fuzzy_skin_point_dist label"
msgid "Fuzzy Skin Point Distance"
msgstr "Vzdálenost bodů při rozmazané výplni"

msgctxt "magic_fuzzy_skin_thickness label"
msgid "Fuzzy Skin Thickness"
msgstr "Tloušťka rozmazaného povrchu"

msgctxt "machine_gcode_flavor label"
msgid "G-code Flavor"
msgstr "Varianta G kódu"

msgctxt "machine_end_gcode description"
msgid ""
"G-code commands to be executed at the very end - separated by \n"
"."
msgstr "Příkazy G-kódu, které mají být provedeny od samého začátku - oddělené \\ n."

msgctxt "machine_start_gcode description"
msgid ""
"G-code commands to be executed at the very start - separated by \n"
"."
msgstr ""
"Příkazy G-kódu, které mají být provedeny na samém konci - oddělené\n"
"."

msgctxt "material_guid description"
msgid "GUID of the material. This is set automatically."
msgstr "GUID materiálu. Je nastaveno automaticky."

msgctxt "gantry_height label"
msgid "Gantry Height"
msgstr "Výška rámu tiskárny"

msgctxt "interlocking_enable label"
msgid "Generate Interlocking Structure"
msgstr "Generovat vzájemné propletení"

msgctxt "support_enable label"
msgid "Generate Support"
msgstr "Generovat podpory"

msgctxt "support_brim_enable description"
msgid "Generate a brim within the support infill regions of the first layer. This brim is printed underneath the support, not around it. Enabling this setting increases the adhesion of support to the build plate."
msgstr "Vytvořte límec v podpůrných výplňových oblastech první vrstvy. Tento límec je vytištěn pod podpěrou, ne kolem ní. Povolením tohoto nastavení se zvýší přilnavost podpory k podložce."

msgctxt "support_interface_enable description"
msgid "Generate a dense interface between the model and the support. This will create a skin at the top of the support on which the model is printed and at the bottom of the support, where it rests on the model."
msgstr "Vytvořte husté rozhraní mezi modelem a podporou. Tím se vytvoří povrch v horní části podpory, na které je model vytištěn, a ve spodní části podpory, kde spočívá na modelu."

msgctxt "support_bottom_enable description"
msgid "Generate a dense slab of material between the bottom of the support and the model. This will create a skin between the model and support."
msgstr "Vytvořte hustou desku materiálu mezi spodní částí nosiče a modelem. Tím vytvoříte vzhled mezi modelem a podporou."

msgctxt "support_roof_enable description"
msgid "Generate a dense slab of material between the top of support and the model. This will create a skin between the model and support."
msgstr "Vytvořte hustou desku materiálu mezi horní částí podpory a modelem. Tím vytvoříte vzhled mezi modelem a podporou."

msgctxt "support_enable description"
msgid "Generate structures to support parts of the model which have overhangs. Without these structures, such parts would collapse during printing."
msgstr "Vytvořte struktury pro podporu částí modelu, které mají přesahy. Bez těchto struktur by se takové části během tisku zhroutily."

msgctxt "machine_buildplate_type option glass"
msgid "Glass"
msgstr "Sklo"

msgctxt "ironing_enabled description"
msgid "Go over the top surface one additional time, but this time extruding very little material. This is meant to melt the plastic on top further, creating a smoother surface. The pressure in the nozzle chamber is kept high so that the creases in the surface are filled with material."
msgstr "Ještě jednou přejděte horní povrch, ale tentokrát vytlačujete jen velmi málo materiálu. To má za cíl roztavit plast nahoře dále a vytvořit hladší povrch. Tlak v komoře trysky je udržován vysoký, takže rýhy na povrchu jsou vyplněny materiálem."

msgctxt "gradual_infill_step_height label"
msgid "Gradual Infill Step Height"
msgstr "Postupná výška kroku výplně"

msgctxt "gradual_infill_steps label"
msgid "Gradual Infill Steps"
msgstr "Postupné kroky výplně"

msgctxt "gradual_support_infill_step_height label"
msgid "Gradual Support Infill Step Height"
msgstr "Výška výplně krokové podpory"

msgctxt "gradual_support_infill_steps label"
msgid "Gradual Support Infill Steps"
msgstr "Postupné kroky vyplňování podpory"

msgctxt "gradual_flow_discretisation_step_size label"
msgid "Gradual flow discretisation step size"
msgstr "Velikost kroku diskretizace postupné změny průtoku"

msgctxt "gradual_flow_enabled label"
msgid "Gradual flow enabled"
msgstr "Postupné změny průtoku povoleny"

msgctxt "max_flow_acceleration label"
msgid "Gradual flow max acceleration"
msgstr "Maximální zrychlení postupných změn průtoku"

msgctxt "cool_min_temperature description"
msgid "Gradually reduce to this temperature when printing at reduced speeds because of minimum layer time."
msgstr "Postupně ochlazuje na tuto teplotu, když se tiskne při snížených rychlostech kvůli minimální doby vrstvy."

msgctxt "infill_pattern option grid"
msgid "Grid"
msgstr "Mřížka"

msgctxt "support_bottom_pattern option grid"
msgid "Grid"
msgstr "Mřížka"

msgctxt "support_interface_pattern option grid"
msgid "Grid"
msgstr "Mřížka"

msgctxt "support_pattern option grid"
msgid "Grid"
msgstr "Mřížka"

msgctxt "support_roof_pattern option grid"
msgid "Grid"
msgstr "Mřížka"

msgctxt "machine_gcode_flavor option Griffin"
msgid "Griffin"
msgstr "Griffin"

msgctxt "group_outer_walls label"
msgid "Group Outer Walls"
msgstr "Seskupit vnější stěny"

msgctxt "infill_pattern option gyroid"
msgid "Gyroid"
msgstr "Gyroid"

msgctxt "support_pattern option gyroid"
msgid "Gyroid"
msgstr "Gyroid"

msgctxt "machine_heated_build_volume label"
msgid "Has Build Volume Temperature Stabilization"
msgstr "Má stabilizaci teploty podložky"

msgctxt "machine_heated_bed label"
msgid "Has Heated Build Plate"
msgstr "Má vyhřívanou podložku"

msgctxt "machine_nozzle_heat_up_speed label"
msgid "Heat Up Speed"
msgstr "Rychlost zahřívání"

msgctxt "machine_heat_zone_length label"
msgid "Heat Zone Length"
msgstr "Délka tepelné zóny"

msgctxt "draft_shield_height description"
msgid "Height limitation of the draft shield. Above this height no draft shield will be printed."
msgstr "Výškové omezení ochranného štítu. Nad touto výškou nebude vytištěn žádný koncept štítu."

msgctxt "z_seam_corner option z_seam_corner_inner"
msgid "Hide Seam"
msgstr "Schovat šev"

msgctxt "z_seam_corner option z_seam_corner_any"
msgid "Hide or Expose Seam"
msgstr "Skrýt nebo ukázat šev"

msgctxt "hole_xy_offset label"
msgid "Hole Horizontal Expansion"
msgstr "Horizontální expanze díry"

msgctxt "hole_xy_offset_max_diameter label"
msgid "Hole Horizontal Expansion Max Diameter"
msgstr "Maximální průměr horizontální expanze díry"

msgctxt "small_hole_max_size description"
msgid "Holes and part outlines with a diameter smaller than this will be printed using Small Feature Speed."
msgstr "Otvory a obrysy součástí s průměrem menším, než je tento, budou vytištěny pomocí funkce Rychlost malých funkcí."

msgctxt "xy_offset label"
msgid "Horizontal Expansion"
msgstr "Horizontální expanze"

msgctxt "material_shrinkage_percentage_xy label"
msgid "Horizontal Scaling Factor Shrinkage Compensation"
msgstr "Horizontální faktor zvětšení pro kompenzaci smrštění"

msgctxt "material_break_preparation_retracted_position description"
msgid "How far the filament can be stretched before it breaks, while heated."
msgstr "Jak daleko může být filament natažen, než se rozbije při zahřátí."

msgctxt "material_anti_ooze_retracted_position description"
msgid "How far the material needs to be retracted before it stops oozing."
msgstr "Jak daleko musí být materiál zasunut, než přestane vytékat."

msgctxt "flow_rate_extrusion_offset_factor description"
msgid "How far to move the filament in order to compensate for changes in flow rate, as a percentage of how far the filament would move in one second of extrusion."
msgstr "Jak daleko posunout vlákno za účelem kompenzace změn průtoku, jako procento toho, jak daleko by se vlákno pohybovalo v jedné sekundě vytlačování."

msgctxt "material_break_retracted_position description"
msgid "How far to retract the filament in order to break it cleanly."
msgstr "Jak daleko se filament zasune tak, aby se čistě přerušil."

msgctxt "material_break_preparation_speed description"
msgid "How fast the filament needs to be retracted just before breaking it off in a retraction."
msgstr "Jak rychle musí být filament zatažen těsně před jeho rozbitím."

msgctxt "material_anti_ooze_retraction_speed description"
msgid "How fast the material needs to be retracted during a filament switch to prevent oozing."
msgstr "Jak rychle je třeba materiál zatáhnout během výměně filamentu, aby se zabránilo vytečení."

msgctxt "material_end_of_filament_purge_speed description"
msgid "How fast to prime the material after replacing an empty spool with a fresh spool of the same material."
msgstr "Jak rychle se materiál po výměně prázdné cívky zastírá čerstvou cívkou stejného materiálu."

msgctxt "material_flush_purge_speed description"
msgid "How fast to prime the material after switching to a different material."
msgstr "Jak rychle se materiál po přepnutí na jiný materiál má rozjet."

msgctxt "material_maximum_park_duration description"
msgid "How long the material can be kept out of dry storage safely."
msgstr "Jak dlouho lze materiál bezpečně uchovávat mimo suché úložiště."

msgctxt "machine_steps_per_mm_x description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the X direction."
msgstr "Kolik kroků krokového motoru povede k jednomu milimetru pohybu ve směru X."

msgctxt "machine_steps_per_mm_y description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Y direction."
msgstr "Kolik kroků krokového motoru povede k jednomu milimetru pohybu ve směru Y."

msgctxt "machine_steps_per_mm_z description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Z direction."
msgstr "Kolik kroků krokového motoru povede k jednomu milimetru pohybu ve směru Z."

msgctxt "machine_steps_per_mm_e description"
msgid "How many steps of the stepper motors will result in moving the feeder wheel by one millimeter around its circumference."
msgstr "Kolik kroků krokového motoru povede k pohybu kolečka feederu o jeden milimetr po jeho obvodu."

msgctxt "material_end_of_filament_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when replacing an empty spool with a fresh spool of the same material."
msgstr "Kolik materiálu se použije k propláchnutí předchozího materiálu z trysky (v délce vlákna) při výměně prázdné cívky za novou cívku ze stejného materiálu."

msgctxt "material_flush_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when switching to a different material."
msgstr "Kolik materiálu použít k vyčištění předchozího materiálu z trysky (v délce vlákna) při přechodu na jiný materiál."

msgctxt "machine_extruders_shared_nozzle_initial_retraction description"
msgid "How much the filament of each extruder is assumed to have been retracted from the shared nozzle tip at the completion of the printer-start gcode script; the value should be equal to or greater than the length of the common part of the nozzle's ducts."
msgstr "Jak daleko je zatažen filament každého extruderu sdílené trysky po dokončení počátečního G kódu tiskárny. Tato hodnota by se měla rovnat nebo být vyšší než je délka společné části vedení trysky."

msgctxt "support_interface_priority description"
msgid "How support interface and support will interact when they overlap. Currently only implemented for support roof."
msgstr "Jak mají rozhraní podpor a podpory interagovat když se překrývají. Aktuálně implementováno pro střechy podpor."

msgctxt "support_tree_min_height_to_model description"
msgid "How tall a branch has to be if it is placed on the model. Prevents small blobs of support. This setting is ignored when a branch is supporting a support roof."
msgstr "Jak vysoká musí větev být, aby mohla být umístěna na modelu. Zabraňuje malým hrudkám tvořícím podpory. Toto nastavení je ignorováno, pokud větev podporuje střechu podpory."

msgctxt "bridge_skin_support_threshold description"
msgid "If a skin region is supported for less than this percentage of its area, print it using the bridge settings. Otherwise it is printed using the normal skin settings."
msgstr "Pokud je oblast povrchu podporována pro méně než toto procento její plochy, vytiskněte ji pomocí nastavení můstku. V opačném případě se vytiskne pomocí běžných nastavení vzhledu."

msgctxt "meshfix_fluid_motion_angle description"
msgid "If a toolpath-segment deviates more than this angle from the general motion it is smoothed."
msgstr ""

msgctxt "bridge_enable_more_layers description"
msgid "If enabled, the second and third layers above the air are printed using the following settings. Otherwise, those layers are printed using the normal settings."
msgstr "Pokud je povoleno, druhá a třetí vrstva nad vzduchem se vytisknou pomocí následujících nastavení. V opačném případě se tyto vrstvy tisknou pomocí běžných nastavení."

msgctxt "wall_transition_filter_distance description"
msgid "If it would be transitioning back and forth between different numbers of walls in quick succession, don't transition at all. Remove transitions if they are closer together than this distance."
msgstr "Pokud by se mělo rychle za sebou přecházet tam a zpět mezi různými počty zdí, nebude se sbíhání provádět. Nastavení odstraní přechody mezi různými počty čar zdí, pokud by byly blíže než tato vzdálenost."

msgctxt "raft_base_margin description"
msgid "If the raft base is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr ""

msgctxt "raft_margin description"
msgid "If the raft is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Pokud je raft povolen, jedná se o další oblast voru kolem modelu, která má také raft. Zvětšení tohoto okraje vytvoří silnější raft při použití více materiálu a ponechání menší plochy pro váš tisk."

msgctxt "raft_interface_margin description"
msgid "If the raft middle is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr ""

msgctxt "raft_surface_margin description"
msgid "If the raft top is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr ""

msgctxt "meshfix_union_all description"
msgid "Ignore the internal geometry arising from overlapping volumes within a mesh and print the volumes as one. This may cause unintended internal cavities to disappear."
msgstr "Ignorujte vnitřní geometrii vznikající z překrývajících se svazků v síti a svazky vytiskněte jako jeden. To může způsobit, že nechtěné vnitřní dutiny zmizí."

msgctxt "material_bed_temp_prepend label"
msgid "Include Build Plate Temperature"
msgstr "Zahrnout teploty podložky"

msgctxt "material_print_temp_prepend label"
msgid "Include Material Temperatures"
msgstr "Zahrnout teploty materiálu"

msgctxt "slicing_tolerance option inclusive"
msgid "Inclusive"
msgstr "Inkluzivní"

msgctxt "infill description"
msgid "Infill"
msgstr "Výplň"

msgctxt "infill label"
msgid "Infill"
msgstr "Výplň"

msgctxt "acceleration_infill label"
msgid "Infill Acceleration"
msgstr "Akcelerace tisku výplně"

msgctxt "infill_before_walls label"
msgid "Infill Before Walls"
msgstr "Výplň před zdmi"

msgctxt "infill_sparse_density label"
msgid "Infill Density"
msgstr "Hustota výplně"

msgctxt "infill_extruder_nr label"
msgid "Infill Extruder"
msgstr "Výplňový extrudér"

msgctxt "infill_material_flow label"
msgid "Infill Flow"
msgstr "Průtok u výplně"

msgctxt "jerk_infill label"
msgid "Infill Jerk"
msgstr "Trh při tisku výplně"

msgctxt "infill_sparse_thickness label"
msgid "Infill Layer Thickness"
msgstr "Tloušťka výplně vrstvy"

msgctxt "infill_angles label"
msgid "Infill Line Directions"
msgstr "Směr výplně"

msgctxt "infill_line_distance label"
msgid "Infill Line Distance"
msgstr "Vzdálenost výplně"

msgctxt "infill_multiplier label"
msgid "Infill Line Multiplier"
msgstr "Náplň řádku linky"

msgctxt "infill_line_width label"
msgid "Infill Line Width"
msgstr "Šířka čáry výplně"

msgctxt "infill_mesh label"
msgid "Infill Mesh"
msgstr "Síť výplně"

msgctxt "infill_support_angle label"
msgid "Infill Overhang Angle"
msgstr "Výplňový přesahový úhel"

msgctxt "infill_overlap_mm label"
msgid "Infill Overlap"
msgstr "Výplň se překrývá"

msgctxt "infill_overlap label"
msgid "Infill Overlap Percentage"
msgstr "Procento překrytí výplně"

msgctxt "infill_pattern label"
msgid "Infill Pattern"
msgstr "Výplňový vzor"

msgctxt "speed_infill label"
msgid "Infill Speed"
msgstr "Rychlost tisku výplně"

msgctxt "infill_support_enabled label"
msgid "Infill Support"
msgstr "Výplňová podpora"

msgctxt "infill_enable_travel_optimization label"
msgid "Infill Travel Optimization"
msgstr "Optimalizace pohybu při tisku výplně"

msgctxt "infill_wipe_dist label"
msgid "Infill Wipe Distance"
msgstr "Vzdálenost výplně"

msgctxt "infill_offset_x label"
msgid "Infill X Offset"
msgstr "X Offset výplně"

msgctxt "infill_offset_y label"
msgid "Infill Y Offset"
msgstr "Y Offset výplně"

msgctxt "initial_bottom_layers label"
msgid "Initial Bottom Layers"
msgstr "Počáteční spodní vrstvy"

msgctxt "cool_fan_speed_0 label"
msgid "Initial Fan Speed"
msgstr "Počáteční rychlost ventilátoru"

msgctxt "acceleration_layer_0 label"
msgid "Initial Layer Acceleration"
msgstr "Akcelerace při první vrstvě"

msgctxt "skin_material_flow_layer_0 label"
msgid "Initial Layer Bottom Flow"
msgstr "Průtok první vrstvy spodku"

msgctxt "support_tree_bp_diameter label"
msgid "Initial Layer Diameter"
msgstr "Průměr počáteční vrstvy"

msgctxt "material_flow_layer_0 label"
msgid "Initial Layer Flow"
msgstr "Průtok při prvotní vrstvě"

msgctxt "layer_height_0 label"
msgid "Initial Layer Height"
msgstr "Výška výchozí vrstvy"

msgctxt "xy_offset_layer_0 label"
msgid "Initial Layer Horizontal Expansion"
msgstr "Počáteční horizontální rozšíření vrstvy"

msgctxt "wall_x_material_flow_layer_0 label"
msgid "Initial Layer Inner Wall Flow"
msgstr "Průtok vnitřních stěn první vrstvy"

msgctxt "jerk_layer_0 label"
msgid "Initial Layer Jerk"
msgstr "Okamžitá rychlost při prvotní vrstvě"

msgctxt "initial_layer_line_width_factor label"
msgid "Initial Layer Line Width"
msgstr "Šířka čáry počáteční vrstvy"

msgctxt "wall_0_material_flow_layer_0 label"
msgid "Initial Layer Outer Wall Flow"
msgstr "Průtok venkovních stěn první vrstvy"

msgctxt "acceleration_print_layer_0 label"
msgid "Initial Layer Print Acceleration"
msgstr "Akcelerace tisku při první vrstvě"

msgctxt "jerk_print_layer_0 label"
msgid "Initial Layer Print Jerk"
msgstr "Okamžitá rychlost při tisku prvotní vrstvy"

msgctxt "speed_print_layer_0 label"
msgid "Initial Layer Print Speed"
msgstr "Rychlost tisku prvotní vrstvy"

msgctxt "speed_layer_0 label"
msgid "Initial Layer Speed"
msgstr "Rychlost prvotní vrstvy"

msgctxt "support_initial_layer_line_distance label"
msgid "Initial Layer Support Line Distance"
msgstr "Počáteční vzdálenost linek podpory"

msgctxt "acceleration_travel_layer_0 label"
msgid "Initial Layer Travel Acceleration"
msgstr "Akcelerace při cestách v první vrstvě"

msgctxt "jerk_travel_layer_0 label"
msgid "Initial Layer Travel Jerk"
msgstr "Okamžitá rychlost při cestování nad prvotní vrstvou"

msgctxt "speed_travel_layer_0 label"
msgid "Initial Layer Travel Speed"
msgstr "Rychlost cestování prvotní vrstvy"

msgctxt "layer_0_z_overlap label"
msgid "Initial Layer Z Overlap"
msgstr "Počáteční překrytí vrstvy Z"

msgctxt "material_initial_print_temperature label"
msgid "Initial Printing Temperature"
msgstr "Počáteční teplota tisku"

msgctxt "layer_0_max_flow_acceleration label"
msgid "Initial layer max flow acceleration"
msgstr "Maximální zrychlení průtoku pro první vrstvu"

msgctxt "acceleration_wall_x label"
msgid "Inner Wall Acceleration"
msgstr "Akcelerace tisku vnitřní zdi"

msgctxt "wall_x_extruder_nr label"
msgid "Inner Wall Extruder"
msgstr "Extruder vnitřní zdi"

msgctxt "jerk_wall_x label"
msgid "Inner Wall Jerk"
msgstr "Okamžitá rychlost při tisku vnitřní zdi"

msgctxt "speed_wall_x label"
msgid "Inner Wall Speed"
msgstr "Rychlost tisku vnitřní zdi"

msgctxt "wall_x_material_flow label"
msgid "Inner Wall(s) Flow"
msgstr "Průtok u vnitřních zdí"

msgctxt "wall_line_width_x label"
msgid "Inner Wall(s) Line Width"
msgstr "Šířka čáry vnitřních stěn"

msgctxt "wall_0_inset description"
msgid "Inset applied to the path of the outer wall. If the outer wall is smaller than the nozzle, and printed after the inner walls, use this offset to get the hole in the nozzle to overlap with the inner walls instead of the outside of the model."
msgstr "Inset aplikovaný na cestu vnější stěny. Pokud je vnější stěna menší než tryska a je vytištěna za vnitřními stěnami, použijte toto odsazení, aby se otvor v trysce překrýval s vnitřními stěnami místo vně modelu."

msgctxt "brim_location option inside"
msgid "Inside Only"
msgstr ""

msgctxt "inset_direction option inside_out"
msgid "Inside To Outside"
msgstr "Zevnitř ven"

msgctxt "support_interface_priority option interface_lines_overwrite_support_area"
msgid "Interface lines preferred"
msgstr "Preferovat čáry rozhraní"

msgctxt "support_interface_priority option interface_area_overwrite_support_area"
msgid "Interface preferred"
msgstr "Preferovat rozhraní"

msgctxt "prime_tower_mode option interleaved"
msgid "Interleaved"
msgstr ""

msgctxt "interlocking_beam_layer_count label"
msgid "Interlocking Beam Layer Count"
msgstr "Počet vrstev paprsků vzájemného propletení"

msgctxt "interlocking_beam_width label"
msgid "Interlocking Beam Width"
msgstr "Šířka paprsku vzájemného propletení"

msgctxt "interlocking_boundary_avoidance label"
msgid "Interlocking Boundary Avoidance"
msgstr "Vyhýbání vzájemného propletení hranicím"

msgctxt "interlocking_depth label"
msgid "Interlocking Depth"
msgstr "Hloubka vzájemného propletení"

msgctxt "interlocking_orientation label"
msgid "Interlocking Structure Orientation"
msgstr "Orientace vzájemného propletení"

msgctxt "ironing_only_highest_layer label"
msgid "Iron Only Highest Layer"
msgstr "Žehlit pouze nejvyšší vrstvu"

msgctxt "acceleration_ironing label"
msgid "Ironing Acceleration"
msgstr "Akcelerace žehlení"

msgctxt "ironing_flow label"
msgid "Ironing Flow"
msgstr "Průtok při žehlení"

msgctxt "ironing_inset label"
msgid "Ironing Inset"
msgstr "Žehlící vložka"

msgctxt "jerk_ironing label"
msgid "Ironing Jerk"
msgstr "Trhnutí při žehlení"

msgctxt "ironing_line_spacing label"
msgid "Ironing Line Spacing"
msgstr "Rozteč žehlicích linek"

msgctxt "ironing_pattern label"
msgid "Ironing Pattern"
msgstr "Vzor žehlení"

msgctxt "speed_ironing label"
msgid "Ironing Speed"
msgstr "Rychlost žehlení"

msgctxt "machine_center_is_zero label"
msgid "Is Center Origin"
msgstr "Je střed počátek"

msgctxt "material_is_support_material label"
msgid "Is support material"
msgstr "Je materiál podpory"

msgctxt "material_crystallinity description"
msgid "Is this material the type that breaks off cleanly when heated (crystalline), or is it the type that produces long intertwined polymer chains (non-crystalline)?"
msgstr "Je tento materiál typem, který se při zahřívání (krystalický) čistě rozpadá, nebo jde o typ, který vytváří dlouhé propletené polymerní řetězce (nekrystalické)?"

msgctxt "material_is_support_material description"
msgid "Is this material typically used as a support material during printing."
msgstr "Je tento materiál typicky při tisku používán jako materiál podpory?"

msgctxt "magic_fuzzy_skin_outside_only description"
msgid "Jitter only the parts' outlines and not the parts' holes."
msgstr "Rozmazat jen okrajové části modelu a žádné díry modelu."

msgctxt "meshfix_keep_open_polygons label"
msgid "Keep Disconnected Faces"
msgstr "Ponechat odpojené plochy"

msgctxt "layer_height label"
msgid "Layer Height"
msgstr "Výška vrstvy"

msgctxt "layer_start_x label"
msgid "Layer Start X"
msgstr "Start vrstvy X"

msgctxt "layer_start_y label"
msgid "Layer Start Y"
msgstr "Start vrstvy Y"

msgctxt "raft_base_thickness description"
msgid "Layer thickness of the base raft layer. This should be a thick layer which sticks firmly to the printer build plate."
msgstr "Tloušťka vrstvy základní vrstvy raftu. Měla by to být silná vrstva, která pevně přilne k podložce tiskárny."

msgctxt "raft_interface_thickness description"
msgid "Layer thickness of the middle raft layer."
msgstr "Tloušťka vrstvy střední vrstvy raftu."

msgctxt "raft_surface_thickness description"
msgid "Layer thickness of the top raft layers."
msgstr "Tloušťka vrstev vrchních vrstev raftu."

msgctxt "support_skip_zag_per_mm description"
msgid "Leave out a connection between support lines once every N millimeter to make the support structure easier to break away."
msgstr "Vynechejte spojení mezi podpůrnými linkami jednou za N milimetr, abyste usnadnili odtržení podpůrné struktury."

msgctxt "z_seam_position option left"
msgid "Left"
msgstr "Levá"

msgctxt "cool_lift_head label"
msgid "Lift Head"
msgstr "Zvednout hlavu"

msgctxt "infill_pattern option lightning"
msgid "Lightning"
msgstr "Bleskový"

msgctxt "lightning_infill_overhang_angle label"
msgid "Lightning Infill Overhang Angle"
msgstr "Úhel převisu bleskové podpory"

msgctxt "lightning_infill_prune_angle label"
msgid "Lightning Infill Prune Angle"
msgstr "Úhel ústupu bleskové vrstvy"

msgctxt "lightning_infill_straightening_angle label"
msgid "Lightning Infill Straightening Angle"
msgstr "Úhel vyrovnávání bleskové vrstvy"

msgctxt "lightning_infill_support_angle label"
msgid "Lightning Infill Support Angle"
msgstr "Úhel podpory bleskové výplně"

msgctxt "support_tree_limit_branch_reach label"
msgid "Limit Branch Reach"
msgstr "Omezení dosahu větví"

msgctxt "support_tree_limit_branch_reach description"
msgid "Limit how far each branch should travel from the point it supports. This can make the support more sturdy, but will increase the amount of branches (and because of that material usage/print time)"
msgstr "Omezuje, jak daleko by měly větve cestovat od bodu, který podporují. Toto nastavení může učinit podporu pevnější, ale zvýší počet větví (a společně s tím také množství použitého materiálu a dobu tisku)"

msgctxt "bv_temp_warn_limit description"
msgid "Limit on Build Volume Temperature warning for detection."
msgstr ""

msgctxt "bv_temp_anomaly_limit description"
msgid "Limit on Build Volume temperature Anomaly for detection."
msgstr ""

msgctxt "print_temp_anomaly_limit description"
msgid "Limit on Print Temperature anomaly for detection."
msgstr ""

msgctxt "print_temp_warn_limit description"
msgid "Limit on Print temperature warning for detection."
msgstr ""

msgctxt "flow_anomaly_limit description"
msgid "Limit on flow anomaly for detection."
msgstr ""

msgctxt "flow_warn_limit description"
msgid "Limit on the flow warning for detection."
msgstr ""

msgctxt "cutting_mesh description"
msgid "Limit the volume of this mesh to within other meshes. You can use this to make certain areas of one mesh print with different settings and with a whole different extruder."
msgstr "Objem této sítě omezte na jiné sítě. Můžete to použít k vytvoření určitých oblastí tisku jednoho oka s různým nastavením as úplně jiným extruderem."

msgctxt "draft_shield_height_limitation option limited"
msgid "Limited"
msgstr "Limitovaná"

msgctxt "line_width label"
msgid "Line Width"
msgstr "Šířka čáry"

msgctxt "infill_pattern option lines"
msgid "Lines"
msgstr "Čáry"

msgctxt "roofing_pattern option lines"
msgid "Lines"
msgstr "Čáry"

msgctxt "support_bottom_pattern option lines"
msgid "Lines"
msgstr "Čáry"

msgctxt "support_interface_pattern option lines"
msgid "Lines"
msgstr "Čáry"

msgctxt "support_pattern option lines"
msgid "Lines"
msgstr "Čáry"

msgctxt "support_roof_pattern option lines"
msgid "Lines"
msgstr "Čáry"

msgctxt "top_bottom_pattern option lines"
msgid "Lines"
msgstr "Čáry"

msgctxt "top_bottom_pattern_0 option lines"
msgid "Lines"
msgstr "Čáry"

msgctxt "machine_gcode_flavor option MACH3"
msgid "Mach3"
msgstr "Mach3"

msgctxt "machine_settings label"
msgid "Machine"
msgstr "Zařízení"

msgctxt "machine_depth label"
msgid "Machine Depth"
msgstr "Hloubka zařízení"

msgctxt "machine_head_with_fans_polygon label"
msgid "Machine Head & Fan Polygon"
msgstr "Polygon hlavy a větráku zařízení"

msgctxt "machine_height label"
msgid "Machine Height"
msgstr "Výška zařízení"

msgctxt "machine_name label"
msgid "Machine Type"
msgstr "Typ zařízení"

msgctxt "machine_width label"
msgid "Machine Width"
msgstr "Šířka zařízení"

msgctxt "machine_settings description"
msgid "Machine specific settings"
msgstr "Specifické nastavení pro zařízení"

msgctxt "conical_overhang_enabled label"
msgid "Make Overhang Printable"
msgstr "Udělat převis tisknutelný"

msgctxt "multiple_mesh_overlap description"
msgid "Make meshes which are touching each other overlap a bit. This makes them bond together better."
msgstr "Síťky, které se navzájem dotýkají, se trochu překrývají. Díky tomu se lepí dohromady."

msgctxt "support_conical_enabled description"
msgid "Make support areas smaller at the bottom than at the overhang."
msgstr "Zmenšete podpůrné oblasti na spodní straně než na převis."

msgctxt "support_mesh_drop_down description"
msgid "Make support everywhere below the support mesh, so that there's no overhang in the support mesh."
msgstr "Podpořte všude pod podpůrnou sítí, aby v podpůrné síti nebyl přesah."

msgctxt "extruder_prime_pos_abs description"
msgid "Make the extruder prime position absolute rather than relative to the last-known location of the head."
msgstr "Zajistěte, aby hlavní poloha extrudéru byla absolutní, nikoli relativní k poslednímu známému umístění hlavy."

msgctxt "layer_0_z_overlap description"
msgid ""
"Make the first and second layer of the model overlap in the Z direction to compensate for the filament lost in the airgap. All models above the first model layer will be shifted down by this amount.\n"
"It may be noted that sometimes the second layer is printed below initial layer because of this setting. This is intended behavior"
msgstr ""

msgctxt "meshfix description"
msgid "Make the meshes more suited for 3D printing."
msgstr "Udělat sítě lépe 3D tisknutelné."

msgctxt "machine_gcode_flavor option Makerbot"
msgid "Makerbot"
msgstr "Makerbot"

msgctxt "support_z_seam_away_from_model description"
msgid "Manage the spatial relationship between the z seam of the support structure and the actual 3D model. This control is crucial as it allows users to ensure the seamless removal of support structures post-printing, without inflicting damage or leaving marks on the printed model."
msgstr ""

msgctxt "machine_gcode_flavor option RepRap (Marlin/Sprinter)"
msgid "Marlin"
msgstr "Marlin"

msgctxt "machine_gcode_flavor option RepRap (Volumetric)"
msgid "Marlin (Volumetric)"
msgstr "Marlin (Volumetric)"

msgctxt "material description"
msgid "Material"
msgstr "Materiál"

msgctxt "material label"
msgid "Material"
msgstr "Materiál"

msgctxt "material_brand label"
msgid "Material Brand"
msgstr ""

msgctxt "material_guid label"
msgid "Material GUID"
msgstr "GUID materiálu"

msgctxt "material_type label"
msgid "Material Type"
msgstr ""

msgctxt "max_extrusion_before_wipe label"
msgid "Material Volume Between Wipes"
msgstr "Objem materiálu mezi čištěními"

msgctxt "retraction_combing_max_distance label"
msgid "Max Comb Distance With No Retract"
msgstr "Maximální vzdálenost Combing-u bez retrakce"

msgctxt "machine_max_acceleration_x label"
msgid "Maximum Acceleration X"
msgstr "Maximální akcelerace X"

msgctxt "machine_max_acceleration_y label"
msgid "Maximum Acceleration Y"
msgstr "Maximální akcelerace Y"

msgctxt "machine_max_acceleration_z label"
msgid "Maximum Acceleration Z"
msgstr "Maximální akcelerace Z"

msgctxt "support_tree_angle label"
msgid "Maximum Branch Angle"
msgstr "Maximální úhel větví"

msgctxt "meshfix_maximum_deviation label"
msgid "Maximum Deviation"
msgstr "Maximální odchylka"

msgctxt "meshfix_maximum_extrusion_area_deviation label"
msgid "Maximum Extrusion Area Deviation"
msgstr "Maximální odchylka plochy extruze"

msgctxt "cool_fan_speed_max label"
msgid "Maximum Fan Speed"
msgstr "Maximální rychlost ventilátoru"

msgctxt "machine_max_acceleration_e label"
msgid "Maximum Filament Acceleration"
msgstr "Maximální akcelerace filamentu"

msgctxt "conical_overhang_angle label"
msgid "Maximum Model Angle"
msgstr "Maximální úhel modelu"

msgctxt "conical_overhang_hole_size label"
msgid "Maximum Overhang Hole Area"
msgstr "Maximální plocha díry pod převisem"

msgctxt "material_maximum_park_duration label"
msgid "Maximum Park Duration"
msgstr "Maximální doba parkingu"

msgctxt "meshfix_maximum_resolution label"
msgid "Maximum Resolution"
msgstr "Maximální rozlišení"

msgctxt "retraction_count_max label"
msgid "Maximum Retraction Count"
msgstr "Maximální pojezd"

msgctxt "max_skin_angle_for_expansion label"
msgid "Maximum Skin Angle for Expansion"
msgstr "Maximální úhel pro rozšíření povrchu"

msgctxt "machine_max_feedrate_e label"
msgid "Maximum Speed E"
msgstr "Maximální rychlost E"

msgctxt "machine_max_feedrate_x label"
msgid "Maximum Speed X"
msgstr "Maximální rychlost X"

msgctxt "machine_max_feedrate_y label"
msgid "Maximum Speed Y"
msgstr "Maximální rychlost Y"

msgctxt "machine_max_feedrate_z label"
msgid "Maximum Speed Z"
msgstr "Maximální rychlost Z"

msgctxt "support_tower_maximum_supported_diameter label"
msgid "Maximum Tower-Supported Diameter"
msgstr "Maximální průměr podporovaný věží"

msgctxt "meshfix_maximum_travel_resolution label"
msgid "Maximum Travel Resolution"
msgstr "Maximální rozlišení pohybu"

msgctxt "max_flow_acceleration description"
msgid "Maximum acceleration for gradual flow changes"
msgstr "Maximální zrychlení pro postupné změny průtoku"

msgctxt "machine_max_acceleration_x description"
msgid "Maximum acceleration for the motor of the X-direction"
msgstr "Maximální zrychlení pro motor ve směru X"

msgctxt "machine_max_acceleration_y description"
msgid "Maximum acceleration for the motor of the Y-direction."
msgstr "Maximální zrychlení pro motor ve směru Y."

msgctxt "machine_max_acceleration_z description"
msgid "Maximum acceleration for the motor of the Z-direction."
msgstr "Maximální zrychlení pro motor ve směru Z."

msgctxt "machine_max_acceleration_e description"
msgid "Maximum acceleration for the motor of the filament."
msgstr "Maximální zrychlení pro motor filamentu."

msgctxt "bridge_sparse_infill_max_density description"
msgid "Maximum density of infill considered to be sparse. Skin over sparse infill is considered to be unsupported and so may be treated as a bridge skin."
msgstr "Maximální hustota výplně považovaná za řídkou. Kůže nad řídkou výplní je považována za nepodporovanou, a proto ji lze považovat za můstkovou kůži."

msgctxt "support_tower_maximum_supported_diameter description"
msgid "Maximum diameter in the X/Y directions of a small area which is to be supported by a specialized support tower."
msgstr "Maximální průměr ve směru X / Y malé plochy, která má být podepřena specializovanou podpůrnou věží."

msgctxt "max_extrusion_before_wipe description"
msgid "Maximum material that can be extruded before another nozzle wipe is initiated. If this value is less than the volume of material required in a layer, the setting has no effect in this layer, i.e. it is limited to one wipe per layer."
msgstr "Maximální materiál, který může být vytlačen před zahájením dalšího stírání trysky. Pokud je tato hodnota menší než objem materiálu potřebného ve vrstvě, nemá nastavení v této vrstvě žádný účinek, tj. Je omezeno na jedno čištění na vrstvu."

msgctxt "multiple_mesh_overlap label"
msgid "Merged Meshes Overlap"
msgstr "Sloučené sítě se překrývají"

msgctxt "meshfix label"
msgid "Mesh Fixes"
msgstr "Opravy sítí"

msgctxt "mesh_position_x label"
msgid "Mesh Position X"
msgstr "Pozice sítě X"

msgctxt "mesh_position_y label"
msgid "Mesh Position Y"
msgstr "Pozice sítě Y"

msgctxt "mesh_position_z label"
msgid "Mesh Position Z"
msgstr "Pozice sítě Z"

msgctxt "infill_mesh_order label"
msgid "Mesh Processing Rank"
msgstr "Pořadí zpracování sítě"

msgctxt "mesh_rotation_matrix label"
msgid "Mesh Rotation Matrix"
msgstr "Matice rotace sítě"

msgctxt "slicing_tolerance option middle"
msgid "Middle"
msgstr "Střední"

msgctxt "support_z_seam_min_distance label"
msgid "Min Z Seam Distance from Model"
msgstr ""

msgctxt "mold_width label"
msgid "Minimal Mold Width"
msgstr "Minimální šířka formy"

msgctxt "machine_min_cool_heat_time_window label"
msgid "Minimal Time Standby Temperature"
msgstr "Minimální doba pohotovostního režimu"

msgctxt "bridge_wall_min_length label"
msgid "Minimum Bridge Wall Length"
msgstr "Minimální délka stěny mostu"

msgctxt "min_even_wall_line_width label"
msgid "Minimum Even Wall Line Width"
msgstr "Minimální šířka párové čáry zdi"

msgctxt "retraction_extrusion_window label"
msgid "Minimum Extrusion Distance Window"
msgstr "Minimální vzdálenost extruze"

msgctxt "min_feature_size label"
msgid "Minimum Feature Size"
msgstr "Minimální velikost částí"

msgctxt "machine_minimum_feedrate label"
msgid "Minimum Feedrate"
msgstr "Minimální feedrate"

msgctxt "support_tree_min_height_to_model label"
msgid "Minimum Height To Model"
msgstr "Minimální výška k modelu"

msgctxt "min_infill_area label"
msgid "Minimum Infill Area"
msgstr "Minimální plocha výplně"

msgctxt "cool_min_layer_time label"
msgid "Minimum Layer Time"
msgstr "Minimální doba vrstvy"

msgctxt "min_odd_wall_line_width label"
msgid "Minimum Odd Wall Line Width"
msgstr "Minimální šířka nepárové čáry zdi"

msgctxt "minimum_polygon_circumference label"
msgid "Minimum Polygon Circumference"
msgstr "Minimální polygonální obvod"

msgctxt "min_skin_width_for_expansion label"
msgid "Minimum Skin Width for Expansion"
msgstr "Minimální úhel pro rozšíření povrchu"

msgctxt "cool_min_speed label"
msgid "Minimum Speed"
msgstr "Minimální rychlost"

msgctxt "minimum_support_area label"
msgid "Minimum Support Area"
msgstr "Minimální oblast pro podporu"

msgctxt "minimum_bottom_area label"
msgid "Minimum Support Floor Area"
msgstr "Minimální oblast pro podporu podlahy"

msgctxt "minimum_interface_area label"
msgid "Minimum Support Interface Area"
msgstr "Minimální plocha pro tisk rozhraní podpory"

msgctxt "minimum_roof_area label"
msgid "Minimum Support Roof Area"
msgstr "Minimální oblast pro podporu střechy"

msgctxt "support_xy_distance_overhang label"
msgid "Minimum Support X/Y Distance"
msgstr "Minimální vzdálenost podpor X/Y"

msgctxt "min_bead_width label"
msgid "Minimum Thin Wall Line Width"
msgstr "Minimální šířka tenkých stěn"

msgctxt "coasting_min_volume label"
msgid "Minimum Volume Before Coasting"
msgstr "Minimální objem před coastingem"

msgctxt "min_wall_line_width label"
msgid "Minimum Wall Line Width"
msgstr "Minimální šířka čáry zdi"

msgctxt "minimum_interface_area description"
msgid "Minimum area size for support interface polygons. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Minimální velikost plochy pro polygony rozhraní podpory. Polygony, které mají plochu menší než tato hodnota, budou vytištěny jako normální podpora."

msgctxt "minimum_support_area description"
msgid "Minimum area size for support polygons. Polygons which have an area smaller than this value will not be generated."
msgstr "Minimální velikost plochy pro podpůrné polygony. Polygony, které mají plochu menší než tato hodnota, nebudou generovány."

msgctxt "minimum_bottom_area description"
msgid "Minimum area size for the floors of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Minimální velikost plochy podlah podpěry. Polygony, které mají plochu menší než tato hodnota, budou vytištěny jako normální podpora."

msgctxt "minimum_roof_area description"
msgid "Minimum area size for the roofs of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Minimální velikost plochy pro střechy podpěry. Polygony, které mají plochu menší než tato hodnota, budou vytištěny jako normální podpora."

msgctxt "layer_0_max_flow_acceleration description"
msgid "Minimum speed for gradual flow changes for the first layer"
msgstr "Minimální rychlost pro postupné změny průtoku pro první vrstvu"

msgctxt "min_feature_size description"
msgid "Minimum thickness of thin features. Model features that are thinner than this value will not be printed, while features thicker than the Minimum Feature Size will be widened to the Minimum Wall Line Width."
msgstr "Minimální tloušťka tenkých částí. Části modelu, které jsou tenčí než tato hodnota nebudou tištěny, zatímco části širší než tato hodnota budou rozšířeny na Minimální šířku čáry zdi."

msgctxt "support_conical_min_width description"
msgid "Minimum width to which the base of the conical support area is reduced. Small widths can lead to unstable support structures."
msgstr "Minimální šířka, na kterou se zmenší základna kuželové nosné plochy. Malé šířky mohou vést k nestabilním podpůrným strukturám."

msgctxt "mold_enabled label"
msgid "Mold"
msgstr "Forma"

msgctxt "mold_angle label"
msgid "Mold Angle"
msgstr "Úhel formy"

msgctxt "mold_roof_height label"
msgid "Mold Roof Height"
msgstr "Výška střechy formy"

msgctxt "ironing_monotonic label"
msgid "Monotonic Ironing Order"
msgstr "Monotónní pořadí žehlení"

msgctxt "raft_surface_monotonic label"
msgid "Monotonic Raft Top Surface Order"
msgstr ""

msgctxt "roofing_monotonic label"
msgid "Monotonic Top Surface Order"
msgstr "Monotónní pořadí horního povrchu"

msgctxt "skin_monotonic label"
msgid "Monotonic Top/Bottom Order"
msgstr "Monotónní pořadí horních / dolních povrchů"

msgctxt "skirt_line_count description"
msgid "Multiple skirt lines help to prime your extrusion better for small models. Setting this to 0 will disable the skirt."
msgstr "Více linek okraje pomáhá vytlačit vaše vytlačování lépe pro malé modely. Nastavení na 0 zakáže okraj."

msgctxt "support_infill_density_multiplier_initial_layer description"
msgid "Multiplier for the infill on the initial layers of the support. Increasing this may help for bed adhesion."
msgstr ""

msgctxt "initial_layer_line_width_factor description"
msgid "Multiplier of the line width on the first layer. Increasing this could improve bed adhesion."
msgstr "Násobitel šířky čáry v první vrstvě. Jejich zvýšení by mohlo zlepšit přilnavost k podložce."

msgctxt "material_no_load_move_factor label"
msgid "No Load Move Factor"
msgstr "Žádný faktor přesunu zatížení"

msgctxt "skin_no_small_gaps_heuristic label"
msgid "No Skin in Z Gaps"
msgstr "Žádný povrch v Z mezerách"

msgctxt "blackmagic description"
msgid "Non-traditional ways to print your models."
msgstr "Netradiční způsoby, jak tisknout vaše modely."

msgctxt "adhesion_type option none"
msgid "None"
msgstr "Žádný"

msgctxt "extra_infill_lines_to_support_skins option none"
msgid "None"
msgstr ""

msgctxt "z_seam_corner option z_seam_corner_none"
msgid "None"
msgstr "Žádný"

msgctxt "magic_mesh_surface_mode option normal"
msgid "Normal"
msgstr "Normální"

msgctxt "prime_tower_mode option normal"
msgid "Normal"
msgstr ""

msgctxt "support_structure option normal"
msgid "Normal"
msgstr "Normální"

msgctxt "meshfix_keep_open_polygons description"
msgid "Normally Cura tries to stitch up small holes in the mesh and remove parts of a layer with big holes. Enabling this option keeps those parts which cannot be stitched. This option should be used as a last resort option when everything else fails to produce proper g-code."
msgstr "Normálně se Cura pokouší spojit malé otvory do sítě a odstranit části vrstvy s velkými otvory. Povolením této možnosti zůstanou zachovány ty části, které nelze sešívat. Tato možnost by měla být použita jako poslední možnost, pokud všechno ostatní nedokáže vytvořit správný g-kód."

msgctxt "retraction_combing option noskin"
msgid "Not in Skin"
msgstr "Ne v povrchu"

msgctxt "retraction_combing option no_outer_surfaces"
msgid "Not on Outer Surface"
msgstr "Ne na vnějším povrchu"

msgctxt "machine_nozzle_expansion_angle label"
msgid "Nozzle Angle"
msgstr "Úhel trysky"

msgctxt "machine_nozzle_size label"
msgid "Nozzle Diameter"
msgstr "Průměr trysky"

msgctxt "nozzle_disallowed_areas label"
msgid "Nozzle Disallowed Areas"
msgstr "Zakázané oblasti pro trysku"

msgctxt "machine_nozzle_id label"
msgid "Nozzle ID"
msgstr "ID trysky"

msgctxt "variant_name"
msgid "Nozzle Size"
msgstr "Velikost trysky"

msgctxt "switch_extruder_extra_prime_amount label"
msgid "Nozzle Switch Extra Prime Amount"
msgstr "Množství materiálu navíc pro změnu trysky"

msgctxt "switch_extruder_prime_speed label"
msgid "Nozzle Switch Prime Speed"
msgstr "Rychlost přepínání trysky"

msgctxt "switch_extruder_retraction_speed label"
msgid "Nozzle Switch Retract Speed"
msgstr "Rychlost retrakce při změně trysky"

msgctxt "switch_extruder_retraction_amount label"
msgid "Nozzle Switch Retraction Distance"
msgstr "Retrakční vzdálenost přepnutí trysek"

msgctxt "switch_extruder_retraction_speeds label"
msgid "Nozzle Switch Retraction Speed"
msgstr "Retrakční rychlost přepnutí trysek"

msgctxt "machine_extruder_count label"
msgid "Number of Extruders"
msgstr "Počet extrůderů"

msgctxt "extruders_enabled_count label"
msgid "Number of Extruders That Are Enabled"
msgstr "Počet povolených extruderů"

msgctxt "speed_slowdown_layers label"
msgid "Number of Slower Layers"
msgstr "Počet pomalých vrstev"

msgctxt "extruders_enabled_count description"
msgid "Number of extruder trains that are enabled; automatically set in software"
msgstr "Počet extruderových strojů, které jsou povoleny; Automaticky nastaveno v softwaru"

msgctxt "machine_extruder_count description"
msgid "Number of extruder trains. An extruder train is the combination of a feeder, bowden tube, and nozzle."
msgstr "Počet extruderových strojů. Vytlačovací souprava je kombinací podavače, bowdenu a trysky."

msgctxt "wipe_repeat_count description"
msgid "Number of times to move the nozzle across the brush."
msgstr "Počet posunů trysky přes kartáč."

msgctxt "gradual_infill_steps description"
msgid "Number of times to reduce the infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Infill Density."
msgstr "Kolikrát se hustota výplně sníží na polovinu, když se dostane dále pod horní povrchy. Oblasti, které jsou blíže k vrchním povrchům, mají vyšší hustotu až do hustoty výplně."

msgctxt "gradual_support_infill_steps description"
msgid "Number of times to reduce the support infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Support Infill Density."
msgstr "Počet opakování, aby se hustota výplně podpory snížila na polovinu, když se dostaneme dále pod horní povrchy. Oblasti, které jsou blíže k vrchním povrchům, mají vyšší hustotu až do podpůrné hustoty výplně."

msgctxt "infill_pattern option tetrahedral"
msgid "Octet"
msgstr "Oktet"

msgctxt "retraction_combing option off"
msgid "Off"
msgstr "Vyp"

msgctxt "mesh_position_x description"
msgid "Offset applied to the object in the x direction."
msgstr "Offset aplikovaný na objekt ve směru x."

msgctxt "mesh_position_y description"
msgid "Offset applied to the object in the y direction."
msgstr "Offset aplikovaný na objekt ve směru y."

msgctxt "mesh_position_z description"
msgid "Offset applied to the object in the z direction. With this you can perform what was used to be called 'Object Sink'."
msgstr "Offset aplikovaný na objekt ve směru z."

msgctxt "machine_use_extruder_offset_to_offset_coords label"
msgid "Offset with Extruder"
msgstr "Offset s extrudérem"

msgctxt "support_tree_rest_preference option buildplate"
msgid "On buildplate when possible"
msgstr "Pokud možno na podložce"

msgctxt "support_tree_rest_preference option graceful"
msgid "On model if required"
msgstr "Klidně i na modelu"

msgctxt "print_sequence option one_at_a_time"
msgid "One at a Time"
msgstr "Pouze jedna"

msgctxt "cool_during_extruder_switch option only_last_extruder"
msgid "Only last extruder"
msgstr ""

msgctxt "retraction_hop_only_when_collides description"
msgid "Only perform a Z Hop when moving over printed parts which cannot be avoided by horizontal motion by Avoid Printed Parts when Traveling."
msgstr "Z-hop provádějte pouze při pohybu po tištěných částech, kterým nelze zabránit vodorovným pohybem pomocí Vyvarujte se potištěných součástí při cestování."

msgctxt "ironing_only_highest_layer description"
msgid "Only perform ironing on the very last layer of the mesh. This saves time if the lower layers don't need a smooth surface finish."
msgstr "Žehlení provádějte pouze na poslední vrstvě sítě. To šetří čas, pokud spodní vrstvy nepotřebují hladký povrch."

msgctxt "ooze_shield_angle label"
msgid "Ooze Shield Angle"
msgstr "Úhel Ooze štítu"

msgctxt "ooze_shield_dist label"
msgid "Ooze Shield Distance"
msgstr "Vzdálenost Ooze štítu"

msgctxt "support_tree_branch_reach_limit label"
msgid "Optimal Branch Range"
msgstr "Optimální dosah větví"

msgctxt "optimize_wall_printing_order label"
msgid "Optimize Wall Printing Order"
msgstr "Optimalizace pořadí tisku stěn"

msgctxt "optimize_wall_printing_order description"
msgid "Optimize the order in which walls are printed so as to reduce the number of retractions and the distance travelled. Most parts will benefit from this being enabled but some may actually take longer so please compare the print time estimates with and without optimization. First layer is not optimized when choosing brim as build plate adhesion type."
msgstr "Optimalizujte pořadí, ve kterém se stěny tisknou, aby se snížil počet retrakcí a ujetá vzdálenost. Většina částí bude mít z tohoto povolení prospěch, ale některé mohou ve skutečnosti trvat déle, proto porovnejte odhady doby tisku s optimalizací a bez ní. První vrstva není optimalizována při výběru límce jako adhezního typu desky."

msgctxt "machine_nozzle_tip_outer_diameter label"
msgid "Outer Nozzle Diameter"
msgstr "Vnější průměr trysky"

msgctxt "acceleration_wall_0 label"
msgid "Outer Wall Acceleration"
msgstr "Akcelerace tisku vnější zdi"

msgctxt "wall_0_acceleration label"
msgid "Outer Wall Acceleration"
msgstr ""

msgctxt "wall_0_deceleration label"
msgid "Outer Wall Deceleration"
msgstr ""

msgctxt "wall_0_end_speed_ratio label"
msgid "Outer Wall End Speed Ratio"
msgstr ""

msgctxt "wall_0_extruder_nr label"
msgid "Outer Wall Extruder"
msgstr "Extruder vnější zdi"

msgctxt "wall_0_material_flow label"
msgid "Outer Wall Flow"
msgstr "Průtok u vnější zdi"

msgctxt "wall_0_inset label"
msgid "Outer Wall Inset"
msgstr "Vnější stěna"

msgctxt "jerk_wall_0 label"
msgid "Outer Wall Jerk"
msgstr "Okamžitá rychlost při tisku vnější zdi"

msgctxt "wall_line_width_0 label"
msgid "Outer Wall Line Width"
msgstr "Šířka čáry vnější stěny"

msgctxt "speed_wall_0 label"
msgid "Outer Wall Speed"
msgstr "Rychlost tisku vnější zdi"

msgctxt "wall_0_speed_split_distance label"
msgid "Outer Wall Speed Split Distance"
msgstr ""

msgctxt "wall_0_start_speed_ratio label"
msgid "Outer Wall Start Speed Ratio"
msgstr ""

msgctxt "wall_0_wipe_dist label"
msgid "Outer Wall Wipe Distance"
msgstr "Vzdálenost stírání vnější stěny"

msgctxt "group_outer_walls description"
msgid "Outer walls of different islands in the same layer are printed in sequence. When enabled the amount of flow changes is limited because walls are printed one type at a time, when disabled the number of travels between islands is reduced because walls in the same islands are grouped."
msgstr "Venkovní stěny různých ostrovů ve stejné vrstvě jsou tisknuty postupně. Když je povoleno, množství změn proudu je omezeno, protože stěny jsou tisknuty po jednom typu najednou, když je zakázáno, počet cest mezi ostrovy se snižuje, protože stěny na stejných ostrovech jsou seskupeny."

msgctxt "brim_location option outside"
msgid "Outside Only"
msgstr ""

msgctxt "inset_direction option outside_in"
msgid "Outside To Inside"
msgstr "Zvenku dovnitř"

msgctxt "wall_overhang_angle label"
msgid "Overhanging Wall Angle"
msgstr "Převislý úhel stěny"

msgctxt "wall_overhang_speed_factor label"
msgid "Overhanging Wall Speed"
msgstr "Rychlost tisku převislé stěny"

msgctxt "wall_overhang_speed_factor description"
msgid "Overhanging walls will be printed at this percentage of their normal print speed."
msgstr "Převislé stěny budou vytištěny v procentech jejich normální rychlosti tisku."

msgctxt "wipe_pause description"
msgid "Pause after the unretract."
msgstr "Pozastavit po vytažení."

msgctxt "bridge_fan_speed description"
msgid "Percentage fan speed to use when printing bridge walls and skin."
msgstr "Procentuální rychlost ventilátoru, která se použije při tisku mostních stěn a povrchu."

msgctxt "bridge_fan_speed_2 description"
msgid "Percentage fan speed to use when printing the second bridge skin layer."
msgstr "Procentuální rychlost ventilátoru, která se použije při tisku druhé vrstvy povrchu mostu."

msgctxt "support_supported_skin_fan_speed description"
msgid "Percentage fan speed to use when printing the skin regions immediately above the support. Using a high fan speed can make the support easier to remove."
msgstr "Procentuální rychlost ventilátoru, která se použije při tisku oblastí kůže bezprostředně nad podporou. Použití vysoké rychlosti ventilátoru může usnadnit odebrání podpory."

msgctxt "bridge_fan_speed_3 description"
msgid "Percentage fan speed to use when printing the third bridge skin layer."
msgstr "Procentuální rychlost ventilátoru, která se použije při tisku třetí vrstvy povrchu mostu."

msgctxt "z_seam_on_vertex description"
msgid "Place the z-seam on a polygon vertex. Switching this off can place the seam between vertices as well. (Keep in mind that this won't override the restrictions on placing the seam on an unsupported overhang.)"
msgstr ""

msgctxt "minimum_polygon_circumference description"
msgid "Polygons in sliced layers that have a circumference smaller than this amount will be filtered out. Lower values lead to higher resolution mesh at the cost of slicing time. It is meant mostly for high resolution SLA printers and very tiny 3D models with a lot of details."
msgstr "Polygony v krájených vrstvách, jejichž obvod je menší než toto množství, budou odfiltrovány. Nižší hodnoty vedou k vyššímu rozlišení ok za cenu krájení. Je určen především pro tiskárny SLA s vysokým rozlišením a velmi malé 3D modely se spoustou detailů."

msgctxt "support_tree_angle_slow label"
msgid "Preferred Branch Angle"
msgstr "Preferovaný úhel větví"

msgctxt "wall_transition_filter_deviation description"
msgid "Prevent transitioning back and forth between one extra wall and one less. This margin extends the range of line widths which follow to [Minimum Wall Line Width - Margin, 2 * Minimum Wall Line Width + Margin]. Increasing this margin reduces the number of transitions, which reduces the number of extrusion starts/stops and travel time. However, large line width variation can lead to under- or overextrusion problems."
msgstr "Zabraňuje přecházení tam a zpět mezi o jednu více a o jednu méně čarami zdí. Tato rezerva rozšíří rozsah možných šířek čar na [Minimální šířka čáry zdi - Rezerva, 2 * Minimální šířka čáry zdi + Rezerva]. Zvýšení této rezervy omezí počet přechodů mezi různými počty čar zdí, což sníží počet rozjezdů/zastavení a také čas cestování. Velké výkyvy v šířce čar však mohou vést k problémům s nedostatečnou nebo přílišnou extruzí."

msgctxt "acceleration_prime_tower label"
msgid "Prime Tower Acceleration"
msgstr "Akcelerace tisku hlavní věže"

msgctxt "prime_tower_brim_enable label"
msgid "Prime Tower Base"
msgstr ""

msgctxt "prime_tower_base_height label"
msgid "Prime Tower Base Height"
msgstr ""

msgctxt "prime_tower_base_size label"
msgid "Prime Tower Base Size"
msgstr ""

msgctxt "prime_tower_base_curve_magnitude label"
msgid "Prime Tower Base Slope"
msgstr ""

msgctxt "prime_tower_flow label"
msgid "Prime Tower Flow"
msgstr "Průtok u hlavní věžě"

msgctxt "jerk_prime_tower label"
msgid "Prime Tower Jerk"
msgstr "Okamžitá rychlost při tisku hlavní věže"

msgctxt "prime_tower_line_width label"
msgid "Prime Tower Line Width"
msgstr "Šířka čáry primární věže"

msgctxt "prime_tower_max_bridging_distance label"
msgid "Prime Tower Maximum Bridging Distance"
msgstr ""

msgctxt "prime_tower_min_shell_thickness label"
msgid "Prime Tower Minimum Shell Thickness"
msgstr ""

msgctxt "prime_tower_min_volume label"
msgid "Prime Tower Minimum Volume"
msgstr "Minimální objem hlavní věže"

msgctxt "prime_tower_raft_base_line_spacing label"
msgid "Prime Tower Raft Line Spacing"
msgstr ""

msgctxt "prime_tower_size label"
msgid "Prime Tower Size"
msgstr "Velikost hlavní věže"

msgctxt "speed_prime_tower label"
msgid "Prime Tower Speed"
msgstr "Rychlost tisku hlavní věže"

msgctxt "prime_tower_mode label"
msgid "Prime Tower Type"
msgstr ""

msgctxt "prime_tower_position_x label"
msgid "Prime Tower X Position"
msgstr "Pozice X hlavní věže"

msgctxt "prime_tower_position_y label"
msgid "Prime Tower Y Position"
msgstr "Pozice Y hlavní věže"

msgctxt "acceleration_print label"
msgid "Print Acceleration"
msgstr "Akcelerace tisku"

msgctxt "variant_name"
msgid "Print Core"
msgstr ""

msgctxt "jerk_print label"
msgid "Print Jerk"
msgstr "Trh při tisku"

msgctxt "ppr label"
msgid "Print Process Reporting"
msgstr ""

msgctxt "print_sequence label"
msgid "Print Sequence"
msgstr "Tisková sekvence"

msgctxt "speed_print label"
msgid "Print Speed"
msgstr "Rychlost tisku"

msgctxt "fill_outline_gaps label"
msgid "Print Thin Walls"
msgstr "Tisk tenkých stěn"

msgctxt "brim_location description"
msgid "Print a brim on the outside of the model, inside, or both. Depending on the model, this helps reducing the amount of brim you need to remove afterwards, while ensuring a proper bed adhesion."
msgstr ""

msgctxt "prime_tower_enable description"
msgid "Print a tower next to the print which serves to prime the material after each nozzle switch."
msgstr "Vytiskněte věž vedle tisku, která slouží k naplnění materiálu po každém přepnutí trysky."

msgctxt "infill_support_enabled description"
msgid "Print infill structures only where tops of the model should be supported. Enabling this reduces print time and material usage, but leads to ununiform object strength."
msgstr "Výplňové struktury tiskněte pouze tam, kde by měly být podporovány vrcholy modelu. Pokud to povolíte, sníží se doba tisku a spotřeba materiálu, ale vede k nestejnoměrné pevnosti objektu."

msgctxt "ironing_monotonic description"
msgid "Print ironing lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Tisknout žehlící linky v takovém pořadí, aby se navazující linky překrývaly ve stejném směru. Tisk zabere trochu více času, ale hladké povrchy budou vypadat více jednolité."

msgctxt "mold_enabled description"
msgid "Print models as a mold, which can be cast in order to get a model which resembles the models on the build plate."
msgstr "Vytiskněte modely jako formu, kterou lze odlít, abyste získali model, který se podobá modelům na podložce."

msgctxt "fill_outline_gaps description"
msgid "Print pieces of the model which are horizontally thinner than the nozzle size."
msgstr "Tiskněte kousky modelu, které jsou vodorovně tenčí než velikost trysek."

msgctxt "raft_surface_monotonic description"
msgid "Print raft top surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes the surface look more consistent, which is also visible on the model bottom surface."
msgstr ""

msgctxt "bridge_skin_speed_2 description"
msgid "Print speed to use when printing the second bridge skin layer."
msgstr "Rychlost tisku, která se použije při tisku druhé vrstvy povrchu mostu."

msgctxt "bridge_skin_speed_3 description"
msgid "Print speed to use when printing the third bridge skin layer."
msgstr "Rychlost tisku, která se použije při tisku třetí vrstvy povrchu mostu."

msgctxt "print_temp_anomaly_limit label"
msgid "Print temperature Limit"
msgstr ""

msgctxt "print_temp_warn_limit label"
msgid "Print temperature Warning"
msgstr ""

msgctxt "infill_before_walls description"
msgid "Print the infill before printing the walls. Printing the walls first may lead to more accurate walls, but overhangs print worse. Printing the infill first leads to sturdier walls, but the infill pattern might sometimes show through the surface."
msgstr "Vytiskněte výplň před tiskem na stěny. První tisk stěn může vést k přesnějším stěnám, ale převisy se zhoršují. Tisk výplně nejprve vede k robustnějším stěnám, ale vzor výplně se někdy může objevit skrz povrch."

msgctxt "roofing_monotonic description"
msgid "Print top surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Tisknout linky horního povrchu v takovém pořadí, aby se navazující linky překrývaly ve stejném směru. Tisk zabere trochu více času, ale hladké povrchy budou vypadat více jednolité."

msgctxt "skin_monotonic description"
msgid "Print top/bottom lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Tisknout horní / dolní linky v takovém pořadí, aby se navazující linky překrývaly ve stejném směru. Tisk zabere trochu více času, ale hladké povrchy budou vypadat více jednolité."

msgctxt "material_print_temperature label"
msgid "Printing Temperature"
msgstr "Teplota při tisku"

msgctxt "material_print_temperature_layer_0 label"
msgid "Printing Temperature Initial Layer"
msgstr "Teplota při tisku první vrstvy"

msgctxt "skirt_height description"
msgid "Printing the innermost skirt line with multiple layers makes it easy to remove the skirt."
msgstr "Tisk vnitřní čáry okraje pomocí více vrstev pomáhá snadnějšímu odstraňování okraje."

msgctxt "alternate_extra_perimeter description"
msgid "Prints an extra wall at every other layer. This way infill gets caught between these extra walls, resulting in stronger prints."
msgstr "Vytiskne další stěnu na každou další vrstvu. Tímto způsobem se výplň zachytí mezi těmito stěnami, což má za následek silnější výtisky."

msgctxt "resolution label"
msgid "Quality"
msgstr "Kvalita"

msgctxt "infill_pattern option quarter_cubic"
msgid "Quarter Cubic"
msgstr "Čtvrtinově krychlový"

msgctxt "adhesion_type option raft"
msgid "Raft"
msgstr "Raft"

msgctxt "raft_airgap label"
msgid "Raft Air Gap"
msgstr "Vzduchový mezera v raftu"

msgctxt "raft_base_margin label"
msgid "Raft Base Extra Margin"
msgstr ""

msgctxt "raft_base_extruder_nr label"
msgid "Raft Base Extruder"
msgstr "Extruder základny raftu"

msgctxt "raft_base_fan_speed label"
msgid "Raft Base Fan Speed"
msgstr "Rychlost ventilátoru při tisku základních vrstev raftu"

msgctxt "raft_base_flow label"
msgid "Raft Base Flow"
msgstr ""

msgctxt "raft_base_infill_overlap_mm label"
msgid "Raft Base Infill Overlap"
msgstr ""

msgctxt "raft_base_infill_overlap label"
msgid "Raft Base Infill Overlap Percentage"
msgstr ""

msgctxt "raft_base_line_spacing label"
msgid "Raft Base Line Spacing"
msgstr "Rozteč základny voru"

msgctxt "raft_base_line_width label"
msgid "Raft Base Line Width"
msgstr "Šířka základní linky raftu"

msgctxt "raft_base_acceleration label"
msgid "Raft Base Print Acceleration"
msgstr "Zrychlení tisku spodku raftu"

msgctxt "raft_base_jerk label"
msgid "Raft Base Print Jerk"
msgstr "Trhnutí při tisku dolní vrstvy raftu"

msgctxt "raft_base_speed label"
msgid "Raft Base Print Speed"
msgstr "Rychlost tisku spodku raftu"

msgctxt "raft_base_smoothing label"
msgid "Raft Base Smoothing"
msgstr ""

msgctxt "raft_base_thickness label"
msgid "Raft Base Thickness"
msgstr "Tloušťka základny raftu"

msgctxt "raft_base_wall_count label"
msgid "Raft Base Wall Count"
msgstr "Počet zdí raftu"

msgctxt "raft_margin label"
msgid "Raft Extra Margin"
msgstr "Místo navíc kolem raftu"

msgctxt "raft_fan_speed label"
msgid "Raft Fan Speed"
msgstr "Rychlost ventilátoru při tisku raftu"

msgctxt "raft_flow label"
msgid "Raft Flow"
msgstr ""

msgctxt "raft_interface_flow label"
msgid "Raft Interface Flow"
msgstr ""

msgctxt "raft_interface_infill_overlap_mm label"
msgid "Raft Interface Infill Overlap"
msgstr ""

msgctxt "raft_interface_infill_overlap label"
msgid "Raft Interface Infill Overlap Percentage"
msgstr ""

msgctxt "raft_interface_z_offset label"
msgid "Raft Interface Z Offset"
msgstr ""

msgctxt "raft_interface_margin label"
msgid "Raft Middle Extra Margin"
msgstr ""

msgctxt "raft_interface_extruder_nr label"
msgid "Raft Middle Extruder"
msgstr "Extruder vnitřku raftu"

msgctxt "raft_interface_fan_speed label"
msgid "Raft Middle Fan Speed"
msgstr "Rychlost ventilátoru při tisku středních vrstev raftu"

msgctxt "raft_interface_layers label"
msgid "Raft Middle Layers"
msgstr "Počet středních vrstev raftu"

msgctxt "raft_interface_line_width label"
msgid "Raft Middle Line Width"
msgstr "Šířka prostřední linky raftu"

msgctxt "raft_interface_acceleration label"
msgid "Raft Middle Print Acceleration"
msgstr "Zrychlení tisku středu raftu"

msgctxt "raft_interface_jerk label"
msgid "Raft Middle Print Jerk"
msgstr "Trhnutí při tisku střední vrstvy raftu"

msgctxt "raft_interface_speed label"
msgid "Raft Middle Print Speed"
msgstr "Rychlost tisku prostředku raftu"

msgctxt "raft_interface_smoothing label"
msgid "Raft Middle Smoothing"
msgstr ""

msgctxt "raft_interface_line_spacing label"
msgid "Raft Middle Spacing"
msgstr "Mezera ve středu raftu"

msgctxt "raft_interface_thickness label"
msgid "Raft Middle Thickness"
msgstr "Tloušťka prostředku raftu"

msgctxt "raft_interface_wall_count label"
msgid "Raft Middle Wall Count"
msgstr ""

msgctxt "raft_acceleration label"
msgid "Raft Print Acceleration"
msgstr "Zrychlení tisku raftu"

msgctxt "raft_jerk label"
msgid "Raft Print Jerk"
msgstr "Trhnutí při tisku raftu"

msgctxt "raft_speed label"
msgid "Raft Print Speed"
msgstr "Rychlost tisku raftu"

msgctxt "raft_smoothing label"
msgid "Raft Smoothing"
msgstr "Vyhlazování raftu"

msgctxt "raft_surface_flow label"
msgid "Raft Surface Flow"
msgstr ""

msgctxt "raft_surface_infill_overlap_mm label"
msgid "Raft Surface Infill Overlap"
msgstr ""

msgctxt "raft_surface_infill_overlap label"
msgid "Raft Surface Infill Overlap Percentage"
msgstr ""

msgctxt "raft_surface_z_offset label"
msgid "Raft Surface Z Offset"
msgstr ""

msgctxt "raft_surface_margin label"
msgid "Raft Top Extra Margin"
msgstr ""

msgctxt "raft_surface_extruder_nr label"
msgid "Raft Top Extruder"
msgstr "Extruder povrchu raftu"

msgctxt "raft_surface_fan_speed label"
msgid "Raft Top Fan Speed"
msgstr "Rychlost ventilátoru při tisku horních vrstev raftu"

msgctxt "raft_surface_thickness label"
msgid "Raft Top Layer Thickness"
msgstr "Tloušťka horní vrstvy raftu"

msgctxt "raft_surface_layers label"
msgid "Raft Top Layers"
msgstr "Vrchní vrstvy raftu"

msgctxt "raft_surface_line_width label"
msgid "Raft Top Line Width"
msgstr "Šířka horní linky raftu"

msgctxt "raft_surface_acceleration label"
msgid "Raft Top Print Acceleration"
msgstr "Zrychlení tisku vrchu raftu"

msgctxt "raft_surface_jerk label"
msgid "Raft Top Print Jerk"
msgstr "Trhnutí při tisku vrchní vrstvy raftu"

msgctxt "raft_surface_speed label"
msgid "Raft Top Print Speed"
msgstr "Rychlost tisku vršku raftu"

msgctxt "raft_surface_smoothing label"
msgid "Raft Top Smoothing"
msgstr ""

msgctxt "raft_surface_line_spacing label"
msgid "Raft Top Spacing"
msgstr "Mezera ve horních vrstvách raftu"

msgctxt "raft_surface_wall_count label"
msgid "Raft Top Wall Count"
msgstr ""

msgctxt "raft_wall_count label"
msgid "Raft Wall Count"
msgstr ""

msgctxt "z_seam_type option random"
msgid "Random"
msgstr "Náhodné"

msgctxt "infill_randomize_start_location label"
msgid "Randomize Infill Start"
msgstr "Náhodné spuštění výplně"

msgctxt "infill_randomize_start_location description"
msgid "Randomize which infill line is printed first. This prevents one segment becoming the strongest, but it does so at the cost of an additional travel move."
msgstr "Náhodně vyberte, který výplňový řádek je vytištěn jako první. To zabraňuje tomu, aby se jeden segment stal nejsilnějším, ale činí to za cenu dalšího pohybu."

msgctxt "magic_fuzzy_skin_enabled description"
msgid "Randomly jitter while printing the outer wall, so that the surface has a rough and fuzzy look."
msgstr "Při tisku na vnější stěnu náhodně chvěte tak, že povrch má drsný a rozmazaný vzhled."

msgctxt "machine_shape option rectangular"
msgid "Rectangular"
msgstr "Obdélníková"

msgctxt "cool_fan_speed_min label"
msgid "Regular Fan Speed"
msgstr "Normální rychlost ventilátoru"

msgctxt "cool_fan_full_at_height label"
msgid "Regular Fan Speed at Height"
msgstr "Pravidelná rychlost ventilátoru ve výšce"

msgctxt "cool_fan_full_layer label"
msgid "Regular Fan Speed at Layer"
msgstr "Normální rychlost ventilátoru ve vrstvě"

msgctxt "cool_min_layer_time_fan_speed_max label"
msgid "Regular/Maximum Fan Speed Threshold"
msgstr "Pravidelná / maximální prahová rychlost ventilátoru"

msgctxt "relative_extrusion label"
msgid "Relative Extrusion"
msgstr "Relativní vytlačování"

msgctxt "meshfix_union_all_remove_holes label"
msgid "Remove All Holes"
msgstr "Odstranit všechny díry"

msgctxt "remove_empty_first_layers label"
msgid "Remove Empty First Layers"
msgstr "Odstraňte prázdné první vrstvy"

msgctxt "carve_multiple_volumes label"
msgid "Remove Mesh Intersection"
msgstr "Odstanit průnik sítí"

msgctxt "raft_base_remove_inside_corners label"
msgid "Remove Raft Base Inside Corners"
msgstr ""

msgctxt "raft_remove_inside_corners label"
msgid "Remove Raft Inside Corners"
msgstr "Odstranit vnitřní rohy raftu"

msgctxt "raft_interface_remove_inside_corners label"
msgid "Remove Raft Middle Inside Corners"
msgstr ""

msgctxt "raft_surface_remove_inside_corners label"
msgid "Remove Raft Top Inside Corners"
msgstr ""

msgctxt "carve_multiple_volumes description"
msgid "Remove areas where multiple meshes are overlapping with each other. This may be used if merged dual material objects overlap with each other."
msgstr "Odstraňte oblasti, kde se více sítí vzájemně překrývají. To lze použít, pokud se sloučené duální hmotné objekty vzájemně překrývají."

msgctxt "remove_empty_first_layers description"
msgid "Remove empty layers beneath the first printed layer if they are present. Disabling this setting can cause empty first layers if the Slicing Tolerance setting is set to Exclusive or Middle."
msgstr "Odstraňte prázdné vrstvy pod první potištěnou vrstvou, pokud jsou přítomny. Deaktivace tohoto nastavení může způsobit prázdné první vrstvy, pokud je nastavení Tolerance řezu nastaveno na Exkluzivní nebo Střední."

msgctxt "raft_base_remove_inside_corners description"
msgid "Remove inside corners from the raft base, causing the raft to become convex."
msgstr ""

msgctxt "raft_interface_remove_inside_corners description"
msgid "Remove inside corners from the raft middle part, causing the raft to become convex."
msgstr ""

msgctxt "raft_surface_remove_inside_corners description"
msgid "Remove inside corners from the raft top part, causing the raft to become convex."
msgstr ""

msgctxt "raft_remove_inside_corners description"
msgid "Remove inside corners from the raft, causing the raft to become convex."
msgstr "Odstranit vnitřní rohy raftu a změnit tak raft v konvexní (vypouklý) tvar."

msgctxt "meshfix_union_all_remove_holes description"
msgid "Remove the holes in each layer and keep only the outside shape. This will ignore any invisible internal geometry. However, it also ignores layer holes which can be viewed from above or below."
msgstr "Odstraňte otvory v každé vrstvě a zachujte pouze vnější tvar. To bude ignorovat jakoukoli neviditelnou vnitřní geometrii. Ignoruje však také díry vrstvy, které lze prohlížet shora nebo zdola."

msgctxt "machine_gcode_flavor option RepRap (RepRap)"
msgid "RepRap"
msgstr "RepRap"

msgctxt "machine_gcode_flavor option Repetier"
msgid "Repetier"
msgstr "Repetier"

msgctxt "skin_outline_count description"
msgid "Replaces the outermost part of the top/bottom pattern with a number of concentric lines. Using one or two lines improves roofs that start on infill material."
msgstr "Nahrazuje nejvzdálenější část horního / spodního vzoru řadou soustředných čar. Použití jedné nebo dvou čar zlepšuje střechy, které začínají na výplňovém materiálu."

msgctxt "ppr description"
msgid "Reporting events that go out of set thresholds"
msgstr ""

msgctxt "reset_flow_duration label"
msgid "Reset flow duration"
msgstr "Doba trvání resetování průtoku"

msgctxt "support_tree_rest_preference label"
msgid "Rest Preference"
msgstr "Preferované umístění podpor"

msgctxt "travel_retract_before_outer_wall label"
msgid "Retract Before Outer Wall"
msgstr "Zasuňte před vnější stěnu"

msgctxt "retract_at_layer_change label"
msgid "Retract at Layer Change"
msgstr "Zasunout při změně vrstvy"

msgctxt "retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "Zasunout vlákno, když se tryska pohybuje po netisknutelné oblasti."

msgctxt "wipe_retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "Zasunout filament, když se tryska pohybuje po netisknuté oblasti."

msgctxt "retract_at_layer_change description"
msgid "Retract the filament when the nozzle is moving to the next layer."
msgstr "Zasuňte vlákno, když se tryska pohybuje do další vrstvy."

msgctxt "retraction_amount label"
msgid "Retraction Distance"
msgstr "Délka zatažení"

msgctxt "retraction_extra_prime_amount label"
msgid "Retraction Extra Prime Amount"
msgstr "Množství zatažení navíc"

msgctxt "retraction_min_travel label"
msgid "Retraction Minimum Travel"
msgstr "Minimální pojezd"

msgctxt "retraction_prime_speed label"
msgid "Retraction Prime Speed"
msgstr "Primární rychlost zatažení"

msgctxt "retraction_retract_speed label"
msgid "Retraction Retract Speed"
msgstr "Rychlost zatažení vlákna"

msgctxt "retraction_speed label"
msgid "Retraction Speed"
msgstr "Rychlost zatažení"

msgctxt "z_seam_position option right"
msgid "Right"
msgstr "Pravá"

msgctxt "machine_scale_fan_speed_zero_to_one label"
msgid "Scale Fan Speed To 0-1"
msgstr "Odstupňovat rychlost ventilátoru mezi 0–1"

msgctxt "machine_scale_fan_speed_zero_to_one description"
msgid "Scale the fan speed to be between 0 and 1 instead of between 0 and 256."
msgstr "Změnit stupnici rychlosti ventilátoru na 0 až 1, namísto 0 až 256."

msgctxt "material_shrinkage_percentage label"
msgid "Scaling Factor Shrinkage Compensation"
msgstr "Faktor zvětšení pro kompenzaci smrštění"

msgctxt "scarf_joint_seam_length label"
msgid "Scarf Seam Length"
msgstr ""

msgctxt "scarf_joint_seam_start_height_ratio label"
msgid "Scarf Seam Start Height"
msgstr ""

msgctxt "scarf_split_distance label"
msgid "Scarf Seam Step Length"
msgstr ""

msgctxt "support_meshes_present label"
msgid "Scene Has Support Meshes"
msgstr "Scéna Má Podpůrné Masky"

msgctxt "z_seam_corner label"
msgid "Seam Corner Preference"
msgstr "Rohová preference švu"

msgctxt "seam_overhang_angle label"
msgid "Seam Overhanging Wall Angle"
msgstr ""

msgctxt "user_defined_print_order_enabled label"
msgid "Set Print Sequence Manually"
msgstr "Nastavit tiskovou sekvenci ručně"

msgctxt "draft_shield_height_limitation description"
msgid "Set the height of the draft shield. Choose to print the draft shield at the full height of the model or at a limited height."
msgstr "Nastavte výšku štítu proti průvanu. Zvolte, zda chcete tisknout štít konceptu v plné výšce modelu nebo v omezené výšce."

msgctxt "dual description"
msgid "Settings used for printing with multiple extruders."
msgstr "Nastavení použitá pro tisk pomocí více extruderů."

msgctxt "command_line_settings description"
msgid "Settings which are only used if CuraEngine isn't called from the Cura frontend."
msgstr "Nastavení, která se používají, pouze pokud není CuraEngine vyvolán z rozhraní Cura."

msgctxt "machine_extruders_shared_nozzle_initial_retraction label"
msgid "Shared Nozzle Initial Retraction"
msgstr "Počáteční retrakce sdílené trysky"

msgctxt "z_seam_type option sharpest_corner"
msgid "Sharpest Corner"
msgstr "Nejostřejší roh"

msgctxt "shell description"
msgid "Shell"
msgstr "Shell"

msgctxt "z_seam_type option shortest"
msgid "Shortest"
msgstr "Nejkratší"

msgctxt "machine_show_variants label"
msgid "Show Machine Variants"
msgstr "Zobrazit varianty zařízení"

msgctxt "skin_edge_support_layers label"
msgid "Skin Edge Support Layers"
msgstr "Vrstvy podpory hrany povrchu"

msgctxt "skin_edge_support_thickness label"
msgid "Skin Edge Support Thickness"
msgstr "Tloušťka podpory hrany povrchu"

msgctxt "expand_skins_expand_distance label"
msgid "Skin Expand Distance"
msgstr "Vzdálenost rozšíření povrchu"

msgctxt "skin_overlap_mm label"
msgid "Skin Overlap"
msgstr "Překrytí povrchu"

msgctxt "skin_overlap label"
msgid "Skin Overlap Percentage"
msgstr "Procentuální překrytí povrchu"

msgctxt "skin_preshrink label"
msgid "Skin Removal Width"
msgstr "Šířka odstranění povrchu"

msgctxt "min_skin_width_for_expansion description"
msgid "Skin areas narrower than this are not expanded. This avoids expanding the narrow skin areas that are created when the model surface has a slope close to the vertical."
msgstr "Oblasti povrchu užší, než je tento, nejsou rozšířeny. Tím se zabrání rozšíření úzkých oblastí vzhledu, které jsou vytvořeny, když má povrch modelu sklon v blízkosti svislé."

msgctxt "support_zag_skip_count description"
msgid "Skip one in every N connection lines to make the support structure easier to break away."
msgstr "Přeskočte jeden v každém N spojovacím vedení, aby se usnadnilo odtržení podpůrné struktury."

msgctxt "support_skip_some_zags description"
msgid "Skip some support line connections to make the support structure easier to break away. This setting is applicable to the Zig Zag support infill pattern."
msgstr "Přeskočte některá připojení podpůrné linky, aby se podpůrná struktura snadněji odtrhla. Toto nastavení je použitelné pro vzor výplně podpory Zig Zag."

msgctxt "adhesion_type option skirt"
msgid "Skirt"
msgstr "Okraj"

msgctxt "skirt_gap label"
msgid "Skirt Distance"
msgstr "Vzdálenost okraj"

msgctxt "skirt_height label"
msgid "Skirt Height"
msgstr "Výška okraje"

msgctxt "skirt_line_count label"
msgid "Skirt Line Count"
msgstr "Počet linek okraje"

msgctxt "acceleration_skirt_brim label"
msgid "Skirt/Brim Acceleration"
msgstr "Akcelerace tisku okraje/límce"

msgctxt "skirt_brim_extruder_nr label"
msgid "Skirt/Brim Extruder"
msgstr "Extruder okraje/límce"

msgctxt "skirt_brim_material_flow label"
msgid "Skirt/Brim Flow"
msgstr "Průtok u okraje/límce"

msgctxt "jerk_skirt_brim label"
msgid "Skirt/Brim Jerk"
msgstr "Okamžitá rychlost při tisku okraje/límce"

msgctxt "skirt_brim_line_width label"
msgid "Skirt/Brim Line Width"
msgstr "Šířka čáry okraje/límce"

msgctxt "skirt_brim_minimal_length label"
msgid "Skirt/Brim Minimum Length"
msgstr "Minimální délka okraje/límce"

msgctxt "skirt_brim_speed label"
msgid "Skirt/Brim Speed"
msgstr "Rychlost tisku okraje/límce"

msgctxt "slicing_tolerance label"
msgid "Slicing Tolerance"
msgstr "Tolerance slicování"

msgctxt "small_feature_speed_factor_0 label"
msgid "Small Feature Initial Layer Speed"
msgstr "Rychlost malých částí v počáteční vrstvě"

msgctxt "small_feature_max_length label"
msgid "Small Feature Max Length"
msgstr "Maximální délka malých částí"

msgctxt "small_feature_speed_factor label"
msgid "Small Feature Speed"
msgstr "Rychlost malých částí"

msgctxt "small_hole_max_size label"
msgid "Small Hole Max Size"
msgstr "Maximální velikost malé díry"

msgctxt "cool_min_temperature label"
msgid "Small Layer Printing Temperature"
msgstr "Teplota tisku malých vrstev"

msgctxt "small_skin_on_surface label"
msgid "Small Top/Bottom On Surface"
msgstr ""

msgctxt "small_skin_width label"
msgid "Small Top/Bottom Width"
msgstr "Šířka malého horního / dolního povrchu"

msgctxt "small_feature_speed_factor_0 description"
msgid "Small features on the first layer will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr "Malé části v první vrstvě budou vytištěny při tomto procentuálním poměru jejich normální rychlosti tisku. Pomalejší tisk může pomoci s přilnavostí a přesností."

msgctxt "small_feature_speed_factor description"
msgid "Small features will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr "Drobné části budou vytištěny v procentech jejich normální rychlosti tisku. Pomalejší tisk může pomoci s přilnavostí a přesností."

msgctxt "small_skin_width description"
msgid "Small top/bottom regions are filled with walls instead of the default top/bottom pattern. This helps to avoids jerky motions. Off for the topmost (air-exposed) layer by default (see 'Small Top/Bottom On Surface')."
msgstr ""

msgctxt "brim_smart_ordering label"
msgid "Smart Brim"
msgstr "Chytrý límec"

msgctxt "z_seam_corner option z_seam_corner_weighted"
msgid "Smart Hiding"
msgstr "Inteligentní skrývání"

msgctxt "smooth_spiralized_contours label"
msgid "Smooth Spiralized Contours"
msgstr "Hladké spiralizované obrysy"

msgctxt "smooth_spiralized_contours description"
msgid "Smooth the spiralized contours to reduce the visibility of the Z seam (the Z seam should be barely visible on the print but will still be visible in the layer view). Note that smoothing will tend to blur fine surface details."
msgstr "Vyhlaďte spiralizované obrysy, aby se snížila viditelnost Z-spoje (Z-spoj by měl být sotva viditelný na výtisku, ale bude stále viditelný v pohledu vrstvy). Všimněte si, že vyhlazení bude mít tendenci rozmazávat jemné detaily povrchu."

msgctxt "retraction_extra_prime_amount description"
msgid "Some material can ooze away during a travel move, which can be compensated for here."
msgstr "Během pohybu může nějaký materiál uniknout pryč, což může být kompenzováno zde."

msgctxt "wipe_retraction_extra_prime_amount description"
msgid "Some material can ooze away during a wipe travel moves, which can be compensated for here."
msgstr "Během pohybu stěrače může nějaký materiál vytéct pryč, což může být kompenzováno zde."

msgctxt "blackmagic label"
msgid "Special Modes"
msgstr "Speciální módy"

msgctxt "speed description"
msgid "Speed"
msgstr "Rychlost"

msgctxt "speed label"
msgid "Speed"
msgstr "Rychlost"

msgctxt "wipe_hop_speed description"
msgid "Speed to move the z-axis during the hop."
msgstr "Rychlost pohybu osy z během hopu."

msgctxt "magic_spiralize label"
msgid "Spiralize Outer Contour"
msgstr "Spiralizujte vnější konturu"

msgctxt "magic_spiralize description"
msgid "Spiralize smooths out the Z move of the outer edge. This will create a steady Z increase over the whole print. This feature turns a solid model into a single walled print with a solid bottom. This feature should only be enabled when each layer only contains a single part."
msgstr "Spiralizace vyhlazuje pohyb Z vnější hrany. Tím se vytvoří stálý nárůst Z v celém tisku. Tato funkce mění pevný model na jednostěnný tisk s plným dnem. Tato funkce by měla být povolena, pouze pokud každá vrstva obsahuje pouze jednu část."

msgctxt "material_standby_temperature label"
msgid "Standby Temperature"
msgstr "Teplota při čekání"

msgctxt "machine_start_gcode label"
msgid "Start G-code"
msgstr "Počáteční G kód"

msgctxt "z_seam_type description"
msgid "Starting point of each path in a layer. When paths in consecutive layers start at the same point a vertical seam may show on the print. When aligning these near a user specified location, the seam is easiest to remove. When placed randomly the inaccuracies at the paths' start will be less noticeable. When taking the shortest path the print will be quicker."
msgstr "Počáteční bod každé cesty ve vrstvě. Když cesty v po sobě jdoucích vrstvách začínají ve stejném bodě, může se na výtisku zobrazit svislý šev. Při jejich zarovnání poblíž uživatelem zadaného umístění je šev nejjednodušší odstranit. Při náhodném umístění budou nepřesnosti na začátku cest méně patrné. Při nejkratší cestě bude tisk rychlejší."

msgctxt "machine_steps_per_mm_e label"
msgid "Steps per Millimeter (E)"
msgstr "Kroků za milimetr (E)"

msgctxt "machine_steps_per_mm_x label"
msgid "Steps per Millimeter (X)"
msgstr "Kroků za milimetr (X)"

msgctxt "machine_steps_per_mm_y label"
msgid "Steps per Millimeter (Y)"
msgstr "Kroků za milimetr (Y)"

msgctxt "machine_steps_per_mm_z label"
msgid "Steps per Millimeter (Z)"
msgstr "Kroků za milimetr (Z)"

msgctxt "support description"
msgid "Support"
msgstr "Podpora"

msgctxt "support label"
msgid "Support"
msgstr "Podpora"

msgctxt "acceleration_support label"
msgid "Support Acceleration"
msgstr "Akcelerace tisku podpor"

msgctxt "support_bottom_distance label"
msgid "Support Bottom Distance"
msgstr "Vzdálenost spodní podpory"

msgctxt "support_bottom_wall_count label"
msgid "Support Bottom Wall Line Count"
msgstr "Počet stěn v podlaze podpor"

msgctxt "support_brim_line_count label"
msgid "Support Brim Line Count"
msgstr "Počet podpůrných čar límce"

msgctxt "support_brim_width label"
msgid "Support Brim Width"
msgstr "Šířka límce podpor"

msgctxt "support_zag_skip_count label"
msgid "Support Chunk Line Count"
msgstr "Počet kusů linek podpory"

msgctxt "support_skip_zag_per_mm label"
msgid "Support Chunk Size"
msgstr "Velikost bloku podpory"

msgctxt "support_infill_rate label"
msgid "Support Density"
msgstr "Hustota podpor"

msgctxt "support_xy_overrides_z label"
msgid "Support Distance Priority"
msgstr "Priorita vzdálenost podpor"

msgctxt "support_extruder_nr label"
msgid "Support Extruder"
msgstr "Extruder pro podpory"

msgctxt "acceleration_support_bottom label"
msgid "Support Floor Acceleration"
msgstr "Akcelerace tisku podpor podlahy"

msgctxt "support_bottom_density label"
msgid "Support Floor Density"
msgstr "Hustota podpor podlahy"

msgctxt "support_bottom_extruder_nr label"
msgid "Support Floor Extruder"
msgstr "Extruder pro podporu podlahy"

msgctxt "support_bottom_material_flow label"
msgid "Support Floor Flow"
msgstr "Průtok u podpor podlahy"

msgctxt "support_bottom_offset label"
msgid "Support Floor Horizontal Expansion"
msgstr "Horizontální expanze podpory podlah"

msgctxt "jerk_support_bottom label"
msgid "Support Floor Jerk"
msgstr "Okamžitá rychlost při tisku podpor podlahy"

msgctxt "support_bottom_angles label"
msgid "Support Floor Line Directions"
msgstr "Směrové linie rozhraní podlahy"

msgctxt "support_bottom_line_distance label"
msgid "Support Floor Line Distance"
msgstr "Vzdálenost linek podpor podlahy"

msgctxt "support_bottom_line_width label"
msgid "Support Floor Line Width"
msgstr "Šířka čáry podpory podlahy"

msgctxt "support_bottom_pattern label"
msgid "Support Floor Pattern"
msgstr "Vzor podpor podlahy"

msgctxt "speed_support_bottom label"
msgid "Support Floor Speed"
msgstr "Rychlost tisku podpor podlahy"

msgctxt "support_bottom_height label"
msgid "Support Floor Thickness"
msgstr "Tloušťka podpor podlahy"

msgctxt "support_material_flow label"
msgid "Support Flow"
msgstr "Průtok u podpor"

msgctxt "support_offset label"
msgid "Support Horizontal Expansion"
msgstr "Expanze horizontálnách podpor"

msgctxt "acceleration_support_infill label"
msgid "Support Infill Acceleration"
msgstr "Akcelerace tisku výplně podpor"

msgctxt "support_infill_density_multiplier_initial_layer label"
msgid "Support Infill Density Multiplier Initial Layer"
msgstr ""

msgctxt "support_infill_extruder_nr label"
msgid "Support Infill Extruder"
msgstr "Extruder pro vnitřní podpory"

msgctxt "jerk_support_infill label"
msgid "Support Infill Jerk"
msgstr "Okamžitá rychlost při tisku výplně podpor"

msgctxt "support_infill_sparse_thickness label"
msgid "Support Infill Layer Thickness"
msgstr "Tloušťka vrstvy výplně podpory"

msgctxt "support_infill_angles label"
msgid "Support Infill Line Directions"
msgstr "Směry podpůrných výplní linek"

msgctxt "speed_support_infill label"
msgid "Support Infill Speed"
msgstr "Rychlost tisku výplně podpor"

msgctxt "acceleration_support_interface label"
msgid "Support Interface Acceleration"
msgstr "Akcelerace tisku rozhraní podpor"

msgctxt "support_interface_density label"
msgid "Support Interface Density"
msgstr "Hustota rozhraní podpor"

msgctxt "support_interface_extruder_nr label"
msgid "Support Interface Extruder"
msgstr "Extruder pro rozhraní podpor"

msgctxt "support_interface_material_flow label"
msgid "Support Interface Flow"
msgstr "Průtok rozhraní podpor"

msgctxt "support_interface_offset label"
msgid "Support Interface Horizontal Expansion"
msgstr "Horizontální rozšíření rozhraní podpor"

msgctxt "jerk_support_interface label"
msgid "Support Interface Jerk"
msgstr "Okamžitá rychlost při tisku rozhraní podpor"

msgctxt "support_interface_angles label"
msgid "Support Interface Line Directions"
msgstr "Směrové linie rozhraní podpor"

msgctxt "support_interface_line_width label"
msgid "Support Interface Line Width"
msgstr "Šířka čáry rozhraní podpor"

msgctxt "support_interface_pattern label"
msgid "Support Interface Pattern"
msgstr "Vzor rozhraní podpor"

msgctxt "support_interface_priority label"
msgid "Support Interface Priority"
msgstr "Priorita rozhraní podpor"

msgctxt "speed_support_interface label"
msgid "Support Interface Speed"
msgstr "Rychlost tisku rozhraní podpor"

msgctxt "support_interface_height label"
msgid "Support Interface Thickness"
msgstr "Tloušťka rozhraní podpor"

msgctxt "support_interface_wall_count label"
msgid "Support Interface Wall Line Count"
msgstr "Počet stěn rozhraní podpor"

msgctxt "jerk_support label"
msgid "Support Jerk"
msgstr "Okamžitá rychlost při tisku podpor"

msgctxt "support_join_distance label"
msgid "Support Join Distance"
msgstr "Vzdálenost propojení podpor"

msgctxt "support_line_distance label"
msgid "Support Line Distance"
msgstr "Vzdálenost mezi linkami podpor"

msgctxt "support_line_width label"
msgid "Support Line Width"
msgstr "Šířka čáry podpory"

msgctxt "support_mesh label"
msgid "Support Mesh"
msgstr "Síť podpor"

msgctxt "support_angle label"
msgid "Support Overhang Angle"
msgstr "Podpora převislého úhlu"

msgctxt "support_pattern label"
msgid "Support Pattern"
msgstr "Vzor podpor"

msgctxt "support_type label"
msgid "Support Placement"
msgstr "Rozmistění podpor"

msgctxt "acceleration_support_roof label"
msgid "Support Roof Acceleration"
msgstr "Akcelerace tisku podpor střechy"

msgctxt "support_roof_density label"
msgid "Support Roof Density"
msgstr "Hustota podpor střechy"

msgctxt "support_roof_extruder_nr label"
msgid "Support Roof Extruder"
msgstr "Extruder pro podporu střech"

msgctxt "support_roof_material_flow label"
msgid "Support Roof Flow"
msgstr "Průtok u podpor střechy"

msgctxt "support_roof_offset label"
msgid "Support Roof Horizontal Expansion"
msgstr "Horizontální expanze podpory střechy"

msgctxt "jerk_support_roof label"
msgid "Support Roof Jerk"
msgstr "Okamžitá rychlost při tisku podpor střechy"

msgctxt "support_roof_angles label"
msgid "Support Roof Line Directions"
msgstr "Směrové linie rozhraní střechy"

msgctxt "support_roof_line_distance label"
msgid "Support Roof Line Distance"
msgstr "Vzdálenost linek podpor střechy"

msgctxt "support_roof_line_width label"
msgid "Support Roof Line Width"
msgstr "Šířka čáry podpory střechy"

msgctxt "support_roof_pattern label"
msgid "Support Roof Pattern"
msgstr "Vzor podpor střechy"

msgctxt "speed_support_roof label"
msgid "Support Roof Speed"
msgstr "Rychlost tisku podpor střechy"

msgctxt "support_roof_height label"
msgid "Support Roof Thickness"
msgstr "Tloušťka podpor střechy"

msgctxt "support_roof_wall_count label"
msgid "Support Roof Wall Line Count"
msgstr "Počet stěn ve střeše podpor"

msgctxt "speed_support label"
msgid "Support Speed"
msgstr "Rychlost tisku podor"

msgctxt "support_bottom_stair_step_height label"
msgid "Support Stair Step Height"
msgstr "Výška schodu podpěrného schodiště"

msgctxt "support_bottom_stair_step_width label"
msgid "Support Stair Step Maximum Width"
msgstr "Maximální šířka schodu podpěrného schodiště"

msgctxt "support_bottom_stair_step_min_slope label"
msgid "Support Stair Step Minimum Slope Angle"
msgstr "Podpora Schodu Minimální Úhel Sklonu"

msgctxt "support_structure label"
msgid "Support Structure"
msgstr "Podpůrná struktura"

msgctxt "support_top_distance label"
msgid "Support Top Distance"
msgstr "Vzdálenost horní podpory"

msgctxt "support_wall_count label"
msgid "Support Wall Line Count"
msgstr "Počet stěn podpor"

msgctxt "support_xy_distance label"
msgid "Support X/Y Distance"
msgstr "Vzdálenost podpor X/Y"

msgctxt "support_z_distance label"
msgid "Support Z Distance"
msgstr "Vzdálenost Z podor"

msgctxt "support_z_seam_away_from_model label"
msgid "Support Z Seam Away from Model"
msgstr ""

msgctxt "support_interface_priority option support_lines_overwrite_interface_area"
msgid "Support lines preferred"
msgstr "Preferovat čáry podpor"

msgctxt "support_interface_priority option support_area_overwrite_interface_area"
msgid "Support preferred"
msgstr "Preferovat podpory"

msgctxt "support_supported_skin_fan_speed label"
msgid "Supported Skin Fan Speed"
msgstr "Rychlost ventilátoru při tisku podpor povrch"

msgctxt "magic_mesh_surface_mode option surface"
msgid "Surface"
msgstr "Povrchový"

msgctxt "material_surface_energy label"
msgid "Surface Energy"
msgstr "Povrchová energie"

msgctxt "magic_mesh_surface_mode label"
msgid "Surface Mode"
msgstr "Povrchový režim"

msgctxt "material_adhesion_tendency description"
msgid "Surface adhesion tendency."
msgstr "Tendence povrchové přilnavosti."

msgctxt "material_surface_energy description"
msgid "Surface energy."
msgstr "Povrchová energie."

msgctxt "brim_smart_ordering description"
msgid "Swap print order of the innermost and second innermost brim lines. This improves brim removal."
msgstr "Prohodí pořadí tisku vnitřní a druhé nejvnitřnější čáry límce. Toto usnadňuje odstraňování límce."

msgctxt "alternate_carve_order description"
msgid "Switch to which mesh intersecting volumes will belong with every layer, so that the overlapping meshes become interwoven. Turning this setting off will cause one of the meshes to obtain all of the volume in the overlap, while it is removed from the other meshes."
msgstr "Přepněte do kterého protínajícího se svazku sítí bude patřit každá vrstva, takže překrývající se očka se protnou. Vypnutí tohoto nastavení způsobí, že jedna z sítí získá veškerý objem v překrytí, zatímco je odstraněna z ostatních sítí."

msgctxt "adaptive_layer_height_threshold description"
msgid "Target horizontal distance between two adjacent layers. Reducing this setting causes thinner layers to be used to bring the edges of the layers closer together."
msgstr "Zaměřte vodorovnou vzdálenost mezi dvěma sousedními vrstvami. Snížení tohoto nastavení způsobí, že se tenčí vrstvy použijí k přibližování okrajů vrstev k sobě."

msgctxt "layer_start_x description"
msgid "The X coordinate of the position near where to find the part to start printing each layer."
msgstr "Souřadnice X pozice poblíž místa, kde má být nalezen díl pro zahájení tisku každé vrstvy."

msgctxt "z_seam_x description"
msgid "The X coordinate of the position near where to start printing each part in a layer."
msgstr "Souřadnice X pozice poblíž místa, kde se má začít tisknout každá část ve vrstvě."

msgctxt "extruder_prime_pos_x description"
msgid "The X coordinate of the position where the nozzle primes at the start of printing."
msgstr "Souřadnice X polohy, ve které tryska naplní tlak na začátku tisku."

msgctxt "layer_start_y description"
msgid "The Y coordinate of the position near where to find the part to start printing each layer."
msgstr "Souřadnice Y pozice poblíž místa, kde má být nalezen díl pro zahájení tisku každé vrstvy."

msgctxt "z_seam_y description"
msgid "The Y coordinate of the position near where to start printing each part in a layer."
msgstr "Souřadnice Y pozice poblíž místa, kde se má začít tisknout každá část ve vrstvě."

msgctxt "extruder_prime_pos_y description"
msgid "The Y coordinate of the position where the nozzle primes at the start of printing."
msgstr "Souřadnice Y polohy, ve které tryska naplní tlak na začátku tisku."

msgctxt "extruder_prime_pos_z description"
msgid "The Z coordinate of the position where the nozzle primes at the start of printing."
msgstr "Souřadnice Z pozice, ve které tryska naplní tlak na začátku tisku."

msgctxt "acceleration_print_layer_0 description"
msgid "The acceleration during the printing of the initial layer."
msgstr "Zrychlení během tisku počáteční vrstvy."

msgctxt "acceleration_layer_0 description"
msgid "The acceleration for the initial layer."
msgstr "Zrychlení počáteční vrstvy."

msgctxt "acceleration_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "Zrychlení pro pohyb se pohybuje v počáteční vrstvě."

msgctxt "jerk_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "Zrychlení pro pohyb se pohybuje v počáteční vrstvě."

msgctxt "acceleration_wall_x description"
msgid "The acceleration with which all inner walls are printed."
msgstr "Zrychlení, kterým jsou potištěny všechny vnitřní stěny."

msgctxt "acceleration_infill description"
msgid "The acceleration with which infill is printed."
msgstr "Zrychlení, kterým je výplň vytištěna."

msgctxt "acceleration_ironing description"
msgid "The acceleration with which ironing is performed."
msgstr "Zrychlení, s nímž se provádí žehlení."

msgctxt "acceleration_print description"
msgid "The acceleration with which printing happens."
msgstr "Zrychlení, s nímž dochází k tisku."

msgctxt "raft_base_acceleration description"
msgid "The acceleration with which the base raft layer is printed."
msgstr "Zrychlení, s nímž je tisknuta základní raftová vrstva."

msgctxt "acceleration_support_bottom description"
msgid "The acceleration with which the floors of support are printed. Printing them at lower acceleration can improve adhesion of support on top of your model."
msgstr "Zrychlení, s nímž se potiskují podlahy podpory. Jejich tisk při nižším zrychlení může zlepšit přilnutí podpory na váš model."

msgctxt "acceleration_support_infill description"
msgid "The acceleration with which the infill of support is printed."
msgstr "Zrychlení, kterým je vytištěna výplň podpory."

msgctxt "raft_interface_acceleration description"
msgid "The acceleration with which the middle raft layer is printed."
msgstr "Zrychlení, kterým je tištěna střední vrstva raftu."

msgctxt "acceleration_wall_0 description"
msgid "The acceleration with which the outermost walls are printed."
msgstr "Zrychlení, kterým se potiskují vnější stěny."

msgctxt "acceleration_prime_tower description"
msgid "The acceleration with which the prime tower is printed."
msgstr "Zrychlení, kterým je vytištěna hlavní věž."

msgctxt "raft_acceleration description"
msgid "The acceleration with which the raft is printed."
msgstr "Zrychlení, s nímž je raft tištěn."

msgctxt "acceleration_support_interface description"
msgid "The acceleration with which the roofs and floors of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "Zrychlení, kterým se potiskují střechy a podlahy podložky. Jejich tisk při nižším zrychlení může zlepšit kvalitu převisu."

msgctxt "acceleration_support_roof description"
msgid "The acceleration with which the roofs of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "Zrychlení, kterým se potiskují střechy podpěry. Jejich tisk při nižším zrychlení může zlepšit kvalitu převisu."

msgctxt "acceleration_skirt_brim description"
msgid "The acceleration with which the skirt and brim are printed. Normally this is done with the initial layer acceleration, but sometimes you might want to print the skirt or brim at a different acceleration."
msgstr "Zrychlení, s nímž jsou okraj a límec vytištěny. Normálně se tak děje s počátečním zrychlením vrstvy, ale někdy budete chtít vytisknout okraj nebo límec při jiném zrychlení."

msgctxt "acceleration_support description"
msgid "The acceleration with which the support structure is printed."
msgstr "Zrychlení, kterým je tisknuta nosná struktura."

msgctxt "raft_surface_acceleration description"
msgid "The acceleration with which the top raft layers are printed."
msgstr "Zrychlení, s nímž se tiskne horní vrstvy raftu."

msgctxt "acceleration_wall_x_roofing description"
msgid "The acceleration with which the top surface inner walls are printed."
msgstr "Zrychlení, kterým jsou tisknuty vnitřní stěny horní plochy."

msgctxt "acceleration_wall_0_roofing description"
msgid "The acceleration with which the top surface outermost walls are printed."
msgstr "Zrychlení, kterým jsou tisknuty nejvíce vnější stěny horní plochy."

msgctxt "acceleration_wall description"
msgid "The acceleration with which the walls are printed."
msgstr "Zrychlení, kterým jsou stěny potištěny."

msgctxt "acceleration_roofing description"
msgid "The acceleration with which top surface skin layers are printed."
msgstr "Zrychlení, kterým se potiskují vrchní povrchové vrstvy."

msgctxt "acceleration_topbottom description"
msgid "The acceleration with which top/bottom layers are printed."
msgstr "Zrychlení, kterým se tisknou horní / dolní vrstvy."

msgctxt "acceleration_travel description"
msgid "The acceleration with which travel moves are made."
msgstr "Zrychlení, kterým se pohybují pohyby."

msgctxt "raft_base_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft base printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr ""

msgctxt "raft_interface_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft interface printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr ""

msgctxt "raft_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr ""

msgctxt "raft_surface_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft surface printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr ""

msgctxt "ironing_flow description"
msgid "The amount of material, relative to a normal skin line, to extrude during ironing. Keeping the nozzle filled helps filling some of the crevices of the top surface, but too much results in overextrusion and blips on the side of the surface."
msgstr "Množství materiálu vzhledem k normální linii kůže, které se během žehlení vytlačuje. Udržování trysky naplněné pomáhá vyplnit některé štěrbiny na horním povrchu, ale příliš mnoho vede k nadměrnému vytlačování a klouzání na straně povrchu."

msgctxt "infill_overlap description"
msgid "The amount of overlap between the infill and the walls as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "Velikost překrytí mezi výplní a stěnami jako procento šířky výplňové linie. Mírné překrytí umožňuje, aby se stěny pevně připojily k výplni."

msgctxt "raft_base_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft base, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr ""

msgctxt "raft_base_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft base. A slight overlap allows the walls to connect firmly to the infill."
msgstr ""

msgctxt "raft_interface_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft interface, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr ""

msgctxt "raft_interface_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft interface. A slight overlap allows the walls to connect firmly to the infill."
msgstr ""

msgctxt "raft_surface_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft surface, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr ""

msgctxt "raft_surface_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft surface. A slight overlap allows the walls to connect firmly to the infill."
msgstr ""

msgctxt "infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls. A slight overlap allows the walls to connect firmly to the infill."
msgstr "Velikost překrytí mezi výplní a stěnami. Mírné překrytí umožňuje, aby se stěny pevně připojily k výplni."

msgctxt "switch_extruder_retraction_amount description"
msgid "The amount of retraction when switching extruders. Set to 0 for no retraction at all. This should generally be the same as the length of the heat zone."
msgstr "Množství zatažení při přepínání extruderů. Nastavit na 0 pro žádné stažení. To by obecně mělo být stejné jako délka tepelné zóny."

msgctxt "machine_nozzle_expansion_angle description"
msgid "The angle between the horizontal plane and the conical part right above the tip of the nozzle."
msgstr "Úhel mezi vodorovnou rovinou a kuželovou částí přímo nad špičkou trysky."

msgctxt "support_tower_roof_angle description"
msgid "The angle of a rooftop of a tower. A higher value results in pointed tower roofs, a lower value results in flattened tower roofs."
msgstr "Úhel střechy věže. Vyšší hodnota vede ke špičatým střechám věží, nižší hodnota vede ke zploštěním střech věží."

msgctxt "mold_angle description"
msgid "The angle of overhang of the outer walls created for the mold. 0° will make the outer shell of the mold vertical, while 90° will make the outside of the model follow the contour of the model."
msgstr "Úhel přesahu vnějších stěn vytvořených pro formu. 0° způsobí, že vnější skořepina formy bude svislá, zatímco 90° způsobí, že vnější strana modelu bude sledovat obrys modelu."

msgctxt "support_tree_branch_diameter_angle description"
msgid "The angle of the branches' diameter as they gradually become thicker towards the bottom. An angle of 0 will cause the branches to have uniform thickness over their length. A bit of an angle can increase stability of the tree support."
msgstr "Úhel, který vytváří průměr větví, jak se větve postupně stávají širší směrem dolů. Nulový úhel způsobí, že budou mít větve stejnou tloušťku po celou svoji délku. Malý úhel může pomoci zvýšit stabilitu stromové podpory."

msgctxt "support_conical_angle description"
msgid "The angle of the tilt of conical support. With 0 degrees being vertical, and 90 degrees being horizontal. Smaller angles cause the support to be more sturdy, but consist of more material. Negative angles cause the base of the support to be wider than the top."
msgstr "Úhel náklonu kuželové podpory. S 0° svislým a 90° vodorovným. Menší úhly způsobují, že podpora je robustnější, ale sestává z více materiálu. Záporné úhly způsobují, že základna podpory je širší než horní část."

msgctxt "magic_fuzzy_skin_point_density description"
msgid "The average density of points introduced on each polygon in a layer. Note that the original points of the polygon are discarded, so a low density results in a reduction of the resolution."
msgstr "Průměrná hustota bodů zavedených na každý mnohoúhelník ve vrstvě. Všimněte si, že původní body mnohoúhelníku jsou zahozeny, takže nízká hustota vede ke snížení rozlišení."

msgctxt "magic_fuzzy_skin_point_dist description"
msgid "The average distance between the random points introduced on each line segment. Note that the original points of the polygon are discarded, so a high smoothness results in a reduction of the resolution. This value must be higher than half the Fuzzy Skin Thickness."
msgstr "Průměrná vzdálenost mezi náhodnými body zavedenými v každém segmentu čáry. Všimněte si, že původní body mnohoúhelníku jsou zahozeny, takže vysoká hladkost vede ke snížení rozlišení. Tato hodnota musí být vyšší než polovina tloušťky rozmazaného povrchu."

msgctxt "material_brand description"
msgid "The brand of material used."
msgstr ""

msgctxt "machine_acceleration description"
msgid "The default acceleration of print head movement."
msgstr "Výchozí zrychlení pohybu tiskové hlavy."

msgctxt "default_material_print_temperature description"
msgid "The default temperature used for printing. This should be the \"base\" temperature of a material. All other print temperatures should use offsets based on this value"
msgstr "Výchozí teplota použitá pro tisk. To by měla být „základní“ teplota materiálu. Všechny ostatní teploty tisku by měly používat odchylky založené na této hodnotě"

msgctxt "default_material_bed_temperature description"
msgid "The default temperature used for the heated build plate. This should be the \"base\" temperature of a build plate. All other print temperatures should use offsets based on this value"
msgstr "Výchozí teplota použitá pro vyhřívanou podložku. To by měla být „základní“ teplota podložky. Všechny ostatní teploty tisku by měly používat odchylky založené na této hodnotě"

msgctxt "bridge_skin_density description"
msgid "The density of the bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "Hustota vrstvy povrchu můstku. Hodnoty menší než 100 zvýší mezery mezi liniemi pokožky."

msgctxt "support_bottom_density description"
msgid "The density of the floors of the support structure. A higher value results in better adhesion of the support on top of the model."
msgstr "Hustota podlah nosné konstrukce. Vyšší hodnota vede k lepší adhezi podpory k horní části modelu."

msgctxt "support_roof_density description"
msgid "The density of the roofs of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Hustota střech nosné konstrukce. Vyšší hodnota má za následek lepší přesahy, ale podpory je těžší odstranit."

msgctxt "bridge_skin_density_2 description"
msgid "The density of the second bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "Hustota druhé vrstvy vrstvy můstku. Hodnoty menší než 100 zvýší mezery mezi liniemi pokožky."

msgctxt "bridge_skin_density_3 description"
msgid "The density of the third bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "Hustota třetí vrstvy vrstvy můstku. Hodnoty menší než 100 zvýší mezery mezi liniemi pokožky."

msgctxt "machine_depth description"
msgid "The depth (Y-direction) of the printable area."
msgstr "Hlouba (Isa Y) plochy k tisku."

msgctxt "support_tower_diameter description"
msgid "The diameter of a special tower."
msgstr "Průměr speciální věže."

msgctxt "support_tree_branch_diameter description"
msgid "The diameter of the thinnest branches of tree support. Thicker branches are more sturdy. Branches towards the base will be thicker than this."
msgstr "Průměr větve stromu podpory Průměr nejtenčí větve stromu podpory. Silnější větve jsou odolnější. Větve směrem k základně budou silnější než tato."

msgctxt "support_tree_tip_diameter description"
msgid "The diameter of the top of the tip of the branches of tree support."
msgstr "Průměr konečků větví stromové podpory."

msgctxt "machine_feeder_wheel_diameter description"
msgid "The diameter of the wheel that drives the material in the feeder."
msgstr "Průměr kola, který pohání materiál v podavači."

msgctxt "support_tree_max_diameter description"
msgid "The diameter of the widest branches of tree support. A thicker trunk is more sturdy; a thinner trunk takes up less space on the build plate."
msgstr "Průměr nejširší větve stromové podpory. Širší kmen je více stabilní; tenší kmen zabírá méně místa na podložce."

msgctxt "adaptive_layer_height_variation_step description"
msgid "The difference in height of the next layer height compared to the previous one."
msgstr "Rozdíl ve výšce další vrstvy ve srovnání s předchozí."

msgctxt "ironing_line_spacing description"
msgid "The distance between the lines of ironing."
msgstr "Vzdálenost mezi čárami žehlení."

msgctxt "support_z_seam_min_distance description"
msgid "The distance between the model and its support structure at the z-axis seam."
msgstr ""

msgctxt "travel_avoid_distance description"
msgid "The distance between the nozzle and already printed parts when avoiding during travel moves."
msgstr "Vzdálenost mezi tryskou a již potištěnými částmi, kterým se hlavy vyvaruje."

msgctxt "raft_base_line_spacing description"
msgid "The distance between the raft lines for the base raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr "Vzdálenost mezi vorovými liniemi pro základní vrstvu raftu. Široký rozestup umožňuje snadné vyjmutí voru z podložky."

msgctxt "raft_interface_line_spacing description"
msgid "The distance between the raft lines for the middle raft layer. The spacing of the middle should be quite wide, while being dense enough to support the top raft layers."
msgstr "Vzdálenost mezi liniemi raftů pro střední vrstvu raftů. Vzdálenost mezi středy by měla být poměrně široká, přičemž by měla být dostatečně hustá, aby podepírala horní vrstvy raftů."

msgctxt "raft_surface_line_spacing description"
msgid "The distance between the raft lines for the top raft layers. The spacing should be equal to the line width, so that the surface is solid."
msgstr "Vzdálenost mezi liniemi raftů pro horní vrstvy raftů. Rozestup by měl být roven šířce čáry, takže povrch je pevný."

msgctxt "prime_tower_raft_base_line_spacing description"
msgid "The distance between the raft lines for the unique prime tower raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr ""

msgctxt "interlocking_depth description"
msgid "The distance from the boundary between models to generate interlocking structure, measured in cells. Too few cells will result in poor adhesion."
msgstr "Vzdálenost od hranic mezi modely, do jaké generovat vzájemně propletené struktury (měřeno v buňkách). Příliš málo buněk způsobí špatnou přilnavost."

msgctxt "brim_width description"
msgid "The distance from the model to the outermost brim line. A larger brim enhances adhesion to the build plate, but also reduces the effective print area."
msgstr "Vzdálenost od modelu k nejzazší linii límce. Větší límec zvyšuje přilnavost k podložce, ale také snižuje efektivní tiskovou plochu."

msgctxt "interlocking_boundary_avoidance description"
msgid "The distance from the outside of a model where interlocking structures will not be generated, measured in cells."
msgstr "Vzdálenost od vnějšku modelu, ve které nebudou vzájemně propletené se struktury generovány. Měřeno v buňkách."

msgctxt "machine_heat_zone_length description"
msgid "The distance from the tip of the nozzle in which heat from the nozzle is transferred to the filament."
msgstr "Vzdálenost od špičky trysky, ve které se teplo z trysky přenáší na filament."

msgctxt "bottom_skin_expand_distance description"
msgid "The distance the bottom skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the skin adhere better to the walls on the layer below. Lower values save amount of material used."
msgstr "Vzdálenost spodního povrchu, který se rozšiřuje do výplně. Vyšší hodnoty umožňují lepší přilnavost povrchu k vzoru výplně a lepší přilnavost povrchu ke stěnám na spodní vrstvě. Nižší hodnoty šetří množství použitého materiálu."

msgctxt "expand_skins_expand_distance description"
msgid "The distance the skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on neighboring layers adhere better to the skin. Lower values save amount of material used."
msgstr "Vzdálenost povrchu je rozšířena do výplně. Vyšší hodnoty umožňují lepší přilnavost povrchu k vzoru výplně a díky tomu lepí přilnavost stěn na sousedních vrstvách k povrchu. Nižší hodnoty šetří množství použitého materiálu."

msgctxt "top_skin_expand_distance description"
msgid "The distance the top skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on the layer above adhere better to the skin. Lower values save amount of material used."
msgstr "Vzdálenost, ve které jsou vrchní vrstvy povrchu rozšířeny do výplně. Vyšší hodnoty umožňují lepší přilnavost povrchu k vzoru výplně a lepší přilnutí stěn nad vrstvou k povrchu. Nižší hodnoty šetří množství použitého materiálu."

msgctxt "wipe_move_distance description"
msgid "The distance to move the head back and forth across the brush."
msgstr "Vzdálenost k pohybu hlavy tam a zpět přes štětec."

msgctxt "lightning_infill_prune_angle description"
msgid "The endpoints of infill lines are shortened to save on material. This setting is the angle of overhang of the endpoints of these lines."
msgstr "Koncové body čar výplně jsou zkracovány pro šetření materiálu. Toto nastavení je úhel převisu koncových bodů těchto čar."

msgctxt "material_extrusion_cool_down_speed description"
msgid "The extra speed by which the nozzle cools while extruding. The same value is used to signify the heat up speed lost when heating up while extruding."
msgstr "Extra rychlost, kterou se tryska během vytlačování ochladí. Stejná hodnota se používá k označení rychlosti zahřívání ztracené při zahřívání během vytlačování."

msgctxt "support_extruder_nr_layer_0 description"
msgid "The extruder train to use for printing the first layer of support infill. This is used in multi-extrusion."
msgstr "Vytlačovací souprava použitá pro tisk první vrstvy výplně podpory. Používá se při vícenásobném vytlačování."

msgctxt "raft_base_extruder_nr description"
msgid "The extruder train to use for printing the first layer of the raft. This is used in multi-extrusion."
msgstr "Vytlačovací souprava použitá pro tisk první vrstvy raftu. Používá se při vícenásobném vytlačování."

msgctxt "support_bottom_extruder_nr description"
msgid "The extruder train to use for printing the floors of the support. This is used in multi-extrusion."
msgstr "Vytlačovací souprava použitá pro tisk podlah podpory. Používá se při vícenásobném vytlačování."

msgctxt "support_infill_extruder_nr description"
msgid "The extruder train to use for printing the infill of the support. This is used in multi-extrusion."
msgstr "Vytlačovací souprava použitá pro tisk výplně podpory. Používá se při vícenásobném vytlačování."

msgctxt "raft_interface_extruder_nr description"
msgid "The extruder train to use for printing the middle layer of the raft. This is used in multi-extrusion."
msgstr "Vytlačovací souprava použitá pro tisk vnitřních vrstev raftu. Používá se při vícenásobném vytlačování."

msgctxt "support_interface_extruder_nr description"
msgid "The extruder train to use for printing the roofs and floors of the support. This is used in multi-extrusion."
msgstr "Vytlačovací souprava použitá pro tisk střech a podlah podpory. Používá se při vícenásobném vytlačování."

msgctxt "support_roof_extruder_nr description"
msgid "The extruder train to use for printing the roofs of the support. This is used in multi-extrusion."
msgstr "Vytlačovací souprava použitá pro tisk střech podpory. Používá se při vícenásobném vytlačování."

msgctxt "skirt_brim_extruder_nr description"
msgid "The extruder train to use for printing the skirt or brim. This is used in multi-extrusion."
msgstr "Vytlačovací souprava použitá pro tisk okraje nebo límce. Používá se při vícenásobném vytlačování."

msgctxt "adhesion_extruder_nr description"
msgid "The extruder train to use for printing the skirt/brim/raft. This is used in multi-extrusion."
msgstr "Vytlačovací souprava použitá pro tisk okraje / límce / raftu. Používá se při vícenásobném vytlačování."

msgctxt "support_extruder_nr description"
msgid "The extruder train to use for printing the support. This is used in multi-extrusion."
msgstr "Vytlačovací souprava použitá pro tisk podpory. Používá se při vícenásobném vytlačování."

msgctxt "raft_surface_extruder_nr description"
msgid "The extruder train to use for printing the top layer(s) of the raft. This is used in multi-extrusion."
msgstr "Vytlačovací souprava použitá pro tisk horní vrstvy (vrstev) raftu. Používá se při vícenásobném vytlačování."

msgctxt "infill_extruder_nr description"
msgid "The extruder train used for printing infill. This is used in multi-extrusion."
msgstr "Vytlačovací souprava použitá pro tisk výplně. Používá se při vícenásobném vytlačování."

msgctxt "wall_x_extruder_nr description"
msgid "The extruder train used for printing the inner walls. This is used in multi-extrusion."
msgstr "Vytlačovací souprava použitá pro tisk vnitřních stěn. Používá se při vícenásobném vytlačování."

msgctxt "wall_0_extruder_nr description"
msgid "The extruder train used for printing the outer wall. This is used in multi-extrusion."
msgstr "Vytlačovací souprava použitá pro tisk vnější stěny. Používá se při vícenásobném vytlačování."

msgctxt "top_bottom_extruder_nr description"
msgid "The extruder train used for printing the top and bottom skin. This is used in multi-extrusion."
msgstr "Vytlačovací souprava použitá pro tisk horní a spodní pokožky. Používá se při vícenásobném vytlačování."

msgctxt "roofing_extruder_nr description"
msgid "The extruder train used for printing the top most skin. This is used in multi-extrusion."
msgstr "Vytlačovací souprava použitá pro tisk nejvyššího povrchu. Používá se při vícenásobném vytlačování."

msgctxt "wall_extruder_nr description"
msgid "The extruder train used for printing the walls. This is used in multi-extrusion."
msgstr "Vytlačovací souprava použitá pro tisk stěn. Používá se při vícenásobném vytlačování."

msgctxt "raft_base_fan_speed description"
msgid "The fan speed for the base raft layer."
msgstr "Rychlost ventilátoru při tisku základních vrstev raftu."

msgctxt "raft_interface_fan_speed description"
msgid "The fan speed for the middle raft layer."
msgstr "Rychlost ventilátoru při tisku středních vrstev raftu."

msgctxt "raft_fan_speed description"
msgid "The fan speed for the raft."
msgstr "Rychlost ventilátoru pro tisk raftu."

msgctxt "raft_surface_fan_speed description"
msgid "The fan speed for the top raft layers."
msgstr "Rychlost ventilátoru při tisku horních vrstev raftu."

msgctxt "cross_infill_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the infill of the print."
msgstr "Umístění souboru obrázku, jehož hodnoty jasu určují minimální hustotu na odpovídajícím místě ve výplni tisku."

msgctxt "cross_support_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the support."
msgstr "Umístění souboru obrázku, jehož hodnoty jasu určují minimální hustotu na odpovídajícím místě v podpoře."

msgctxt "speed_slowdown_layers description"
msgid "The first few layers are printed slower than the rest of the model, to get better adhesion to the build plate and improve the overall success rate of prints. The speed is gradually increased over these layers."
msgstr "Prvních několik vrstev je vytištěno pomaleji než zbytek modelu, aby se dosáhlo lepší přilnavosti k sestavovací desce a zlepšila se celková úspěšnost tisků. Rychlost se v těchto vrstvách postupně zvyšuje."

msgctxt "raft_airgap description"
msgid "The gap between the final raft layer and the first layer of the model. Only the first layer is raised by this amount to lower the bonding between the raft layer and the model. Makes it easier to peel off the raft."
msgstr "Mezera mezi finální vrstvou raftu a první vrstvou modelu. Pouze první vrstva se zvýší o tuto částku, aby se snížilo spojení mezi vorovou vrstvou a modelem. Usnadňuje oloupání voru."

msgctxt "machine_height description"
msgid "The height (Z-direction) of the printable area."
msgstr "Výška (Osa Z) plochy k tisku."

msgctxt "mold_roof_height description"
msgid "The height above horizontal parts in your model which to print mold."
msgstr "Výška nad vodorovnými částmi modelu, které chcete vytisknout."

msgctxt "build_fan_full_at_height description"
msgid "The height at which the fans spin on regular fan speed. At the layers below the fan speed gradually increases from Initial Fan Speed to Regular Fan Speed."
msgstr ""

msgctxt "cool_fan_full_at_height description"
msgid "The height at which the fans spin on regular fan speed. At the layers below the fan speed gradually increases from Initial Fan Speed to Regular Fan Speed."
msgstr "Výška, při které se otáčejí ventilátory při normální rychlosti ventilátoru. Ve vrstvách pod rychlostí ventilátoru se postupně zvyšuje z počáteční rychlosti ventilátoru na normální rychlost ventilátoru."

msgctxt "gantry_height description"
msgid "The height difference between the tip of the nozzle and the gantry system (X and Y axes)."
msgstr "Výškový rozdíl mezi špičkou trysky a portálovým systémem (osy X a Y)."

msgctxt "retraction_hop_after_extruder_switch_height description"
msgid "The height difference when performing a Z Hop after extruder switch."
msgstr "Výškový rozdíl při provádění Z Hopu po přepnutí extruderu."

msgctxt "retraction_hop description"
msgid "The height difference when performing a Z Hop."
msgstr "Výškový rozdíl při provádění Z-hopu."

msgctxt "wipe_hop_amount description"
msgid "The height difference when performing a Z Hop."
msgstr "Výškový rozdíl při provádění Z-hopu."

msgctxt "layer_height description"
msgid "The height of each layer in mm. Higher values produce faster prints in lower resolution, lower values produce slower prints in higher resolution."
msgstr "Výška každé vrstvy v mm. Vyšší hodnoty produkují rychlejší výtisky v nižším rozlišení, nižší hodnoty produkují pomalejší výtisky ve vyšším rozlišení."

msgctxt "gradual_infill_step_height description"
msgid "The height of infill of a given density before switching to half the density."
msgstr "Výška výplně dané hustoty před přepnutím na polovinu hustoty."

msgctxt "gradual_support_infill_step_height description"
msgid "The height of support infill of a given density before switching to half the density."
msgstr "Výška podpůrné výplně dané hustoty před přepnutím na polovinu hustoty."

msgctxt "interlocking_beam_layer_count description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr "Výška paprsků vzájemného propletení. Měřeno v počtu vrstev. Méně vrstev způsobí větší pevnost, ale zvýší náchylnost k vadám."

msgctxt "interlocking_orientation description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr "Výška paprsků vzájemného propletení. Měřeno v počtu vrstev. Méně vrstev způsobí větší pevnost, ale zvýší náchylnost k vadám."

msgctxt "layer_height_0 description"
msgid "The height of the initial layer in mm. A thicker initial layer makes adhesion to the build plate easier."
msgstr "Výška počáteční vrstvy v mm. Silnější počáteční vrstva usnadňuje přilnavost k montážní desce."

msgctxt "prime_tower_base_height description"
msgid "The height of the prime tower base. Increasing this value will result in a more sturdy prime tower because the base will be wider. If this setting is too low, the prime tower will not have a sturdy base."
msgstr ""

msgctxt "support_bottom_stair_step_height description"
msgid "The height of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures. Set to zero to turn off the stair-like behaviour."
msgstr "Výška stupňů schodišťového dna podpory spočívá na modelu. Nízká hodnota ztěžuje odstranění podpory, ale příliš vysoké hodnoty mohou vést k nestabilním podpůrným strukturám. Nastavením na nulu vypnete chování podobné schodišti."

msgctxt "brim_gap description"
msgid "The horizontal distance between the first brim line and the outline of the first layer of the print. A small gap can make the brim easier to remove while still providing the thermal benefits."
msgstr "Vodorovná vzdálenost mezi první čarou límce a obrysem první vrstvy tisku. Malá mezera může usnadnit demontáž límce a přitom poskytovat tepelné výhody."

msgctxt "skirt_gap description"
msgid ""
"The horizontal distance between the skirt and the first layer of the print.\n"
"This is the minimum distance. Multiple skirt lines will extend outwards from this distance."
msgstr ""
"Vodorovná vzdálenost mezi okrajem a první vrstvou tisku.\n"
"Toto je minimální vzdálenost. Z této vzdálenosti se bude rozprostírat více linek okraje."

msgctxt "lightning_infill_straightening_angle description"
msgid "The infill lines are straightened out to save on printing time. This is the maximum angle of overhang allowed across the length of the infill line."
msgstr "Čáry výplně jsou vyrovnávány, aby se snížila doba tisku. Toto je maximální dovolený úhel převisu podél čáry výplně."

msgctxt "infill_offset_x description"
msgid "The infill pattern is moved this distance along the X axis."
msgstr "Výplňový vzor se pohybuje touto vzdáleností podél osy X."

msgctxt "infill_offset_y description"
msgid "The infill pattern is moved this distance along the Y axis."
msgstr "Výplňový vzor se pohybuje touto vzdáleností podél osy Y."

msgctxt "machine_nozzle_size description"
msgid "The inner diameter of the nozzle. Change this setting when using a non-standard nozzle size."
msgstr "Vnitřní průměr trysky. Změňte toto nastavení pokud používáte nestandardní velikost trysky."

msgctxt "raft_base_jerk description"
msgid "The jerk with which the base raft layer is printed."
msgstr "Trhnutí, kterým je tisknuta základní vrstva raftu."

msgctxt "raft_interface_jerk description"
msgid "The jerk with which the middle raft layer is printed."
msgstr "Trhnutí, kterým je tisknuta střední vrstva raftu."

msgctxt "raft_jerk description"
msgid "The jerk with which the raft is printed."
msgstr "Trhnutí, při kterém je raft tištěn."

msgctxt "raft_surface_jerk description"
msgid "The jerk with which the top raft layers are printed."
msgstr "Trhnutí, kterým se tisknou horní vrstvy raftu."

msgctxt "bottom_skin_preshrink description"
msgid "The largest width of bottom skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing bottom skin at slanted surfaces in the model."
msgstr "Největší šířka spodních částí povrchu, které mají být odstraněny. Každá oblast povrchu menší než tato hodnota zmizí. To může pomoci omezit množství času a materiálu stráveného tiskem spodní vrstvy na šikmých plochách v modelu."

msgctxt "skin_preshrink description"
msgid "The largest width of skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top/bottom skin at slanted surfaces in the model."
msgstr "Největší šířka oblastí povrchu, které mají být odstraněny. Každá oblast povrchu menší než tato hodnota zmizí. To může pomoci omezit množství času a materiálu stráveného tiskem vrchní / spodní kůže na šikmých plochách v modelu."

msgctxt "top_skin_preshrink description"
msgid "The largest width of top skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top skin at slanted surfaces in the model."
msgstr "Největší šířka horních oblastí povrchu, které mají být odstraněny. Každá oblast povrchu menší než tato hodnota zmizí. To může pomoci omezit množství času a materiálu stráveného tiskem vrchní kůže na šikmých plochách v modelu."

msgctxt "build_fan_full_layer description"
msgid "The layer at which the build fans spin on full fan speed. This value is calculated and rounded to a whole number."
msgstr ""

msgctxt "cool_fan_full_layer description"
msgid "The layer at which the fans spin on regular fan speed. If regular fan speed at height is set, this value is calculated and rounded to a whole number."
msgstr "Vrstva, ve které se ventilátory otáčejí běžnou rychlostí ventilátoru. Pokud je nastavena normální rychlost ventilátoru ve výšce, je tato hodnota vypočítána a zaokrouhlena na celé číslo."

msgctxt "cool_min_layer_time_fan_speed_max description"
msgid "The layer time which sets the threshold between regular fan speed and maximum fan speed. Layers that print slower than this time use regular fan speed. For faster layers the fan speed gradually increases towards the maximum fan speed."
msgstr "Čas vrstvy, který nastavuje práh mezi normální rychlostí ventilátoru a maximální rychlostí ventilátoru. Vrstvy, které se tisknou pomaleji než tentokrát, používají běžnou rychlost ventilátoru. U rychlejších vrstev se rychlost ventilátoru postupně zvyšuje směrem k maximální rychlosti ventilátoru."

msgctxt "retraction_amount description"
msgid "The length of material retracted during a retraction move."
msgstr "Délka materiálu zasunutého během pohybu zasunutí."

msgctxt "prime_tower_base_curve_magnitude description"
msgid "The magnitude factor used for the slope of the prime tower base. If you increase this value, the base will become slimmer. If you decrease it, the base will become thicker."
msgstr ""

msgctxt "machine_buildplate_type description"
msgid "The material of the build plate installed on the printer."
msgstr "Materiál podložky nainstalované na tiskárně."

msgctxt "adaptive_layer_height_variation description"
msgid "The maximum allowed height different from the base layer height."
msgstr "Maximální povolená výška se liší od výšky základní vrstvy."

msgctxt "ooze_shield_angle description"
msgid "The maximum angle a part in the ooze shield will have. With 0 degrees being vertical, and 90 degrees being horizontal. A smaller angle leads to less failed ooze shields, but more material."
msgstr "Maximální úhel, který bude mít část štítu. S 0° svislým a 90° vodorovným. Menší úhel vede k méně poškozeným štítům, ale více materiálu."

msgctxt "conical_overhang_angle description"
msgid "The maximum angle of overhangs after the they have been made printable. At a value of 0° all overhangs are replaced by a piece of model connected to the build plate, 90° will not change the model in any way."
msgstr "Maximální úhel přesahů po jejich tisku. Při hodnotě 0 ° jsou všechny převisy nahrazeny kusem modelu připojeným k podložce, 90 ° model nijak nijak nezmění."

msgctxt "support_tree_angle description"
msgid "The maximum angle of the branches while they grow around the model. Use a lower angle to make them more vertical and more stable. Use a higher angle to be able to have more reach."
msgstr "Maximální úhel větví, které rostou okolo modelu. Použijte nižší úhel, aby byly větve více vertikální a stabilní. Použijte vyšší úhel, aby měly větve větší dosah."

msgctxt "conical_overhang_hole_size description"
msgid "The maximum area of a hole in the base of the model before it's removed by Make Overhang Printable.  Holes smaller than this will be retained.  A value of 0 mm² will fill all holes in the models base."
msgstr "Maximální plocha díry v základně modelu, která nebude odstraněna funkcí „Udělat převis tisknutelný“. Menší díry budou zachovány. Hodnota 0 mm² způsobí vyplnění všech děr v základně modelu."

msgctxt "meshfix_maximum_deviation description"
msgid "The maximum deviation allowed when reducing the resolution for the Maximum Resolution setting. If you increase this, the print will be less accurate, but the g-code will be smaller. Maximum Deviation is a limit for Maximum Resolution, so if the two conflict the Maximum Deviation will always be held true."
msgstr "Maximální odchylka povolená při snižování rozlišení pro nastavení Maximální rozlišení. Pokud toto zvýšíte, bude tisk méně přesný, ale g-kód bude menší. Maximální odchylka je limit pro maximální rozlišení, takže pokud dojde ke konfliktu, bude maximální odchylka vždy platná."

msgctxt "support_join_distance description"
msgid "The maximum distance between support structures in the X/Y directions. When separate structures are closer together than this value, the structures merge into one."
msgstr "Maximální vzdálenost mezi podpůrnými strukturami ve směru X / Y. Když jsou oddělené struktury blíže k sobě než tato hodnota, struktury se sloučí do jedné."

msgctxt "flow_rate_max_extrusion_offset description"
msgid "The maximum distance in mm to move the filament to compensate for changes in flow rate."
msgstr "Maximální vzdálenost v mm pro pohyb vlákna za účelem kompenzace změn průtoku."

msgctxt "meshfix_maximum_extrusion_area_deviation description"
msgid "The maximum extrusion area deviation allowed when removing intermediate points from a straight line. An intermediate point may serve as width-changing point in a long straight line. Therefore, if it is removed, it will cause the line to have a uniform width and, as a result, lose (or gain) a bit of extrusion area. If you increase this you may notice slight under- (or over-) extrusion in between straight parallel walls, as more intermediate width-changing points will be allowed to be removed. Your print will be less accurate, but the g-code will be smaller."
msgstr "Maximální dovolená odchylka plochy extruze při odstraňování mezilehlých bodů na rovné čáře. Mezilehlý bod může sloužit jako místo, kde se mění tloušťka dlouhé přímé čáry. Proto, pokud je odstraněn, bude mít tato čára jednotnou šířku a následkem toho ztratí (nebo získá) kousek plochy extruze. Při zvýšení tohoto nastavení můžete zaznamenat mírnou podextruzi nebo nadměrnou extruzi mezi rovnými rovnoběžnými zdmi, jelikož bude dovoleno odstranit více mezilehlých bodů měnících šířku čáry. Vaše výtisky budou méně přesně, ale g-kód bude menší."

msgctxt "jerk_print_layer_0 description"
msgid "The maximum instantaneous velocity change during the printing of the initial layer."
msgstr "Maximální okamžitá změna rychlosti během tisku počáteční vrstvy."

msgctxt "jerk_print description"
msgid "The maximum instantaneous velocity change of the print head."
msgstr "Maximální okamžitá změna rychlosti tiskové hlavy."

msgctxt "jerk_ironing description"
msgid "The maximum instantaneous velocity change while performing ironing."
msgstr "Maximální okamžitá změna rychlosti při provádění žehlení."

msgctxt "jerk_wall_x description"
msgid "The maximum instantaneous velocity change with which all inner walls are printed."
msgstr "Maximální okamžitá změna rychlosti, se kterou se tisknou všechny vnitřní stěny."

msgctxt "jerk_infill description"
msgid "The maximum instantaneous velocity change with which infill is printed."
msgstr "Maximální okamžitá změna rychlosti tisku výplně."

msgctxt "jerk_support_bottom description"
msgid "The maximum instantaneous velocity change with which the floors of support are printed."
msgstr "Maximální okamžitá změna rychlosti, se kterou se potiskují podlahy podpory."

msgctxt "jerk_support_infill description"
msgid "The maximum instantaneous velocity change with which the infill of support is printed."
msgstr "Maximální okamžitá změna rychlosti, s níž je vytištěna výplň podpory."

msgctxt "jerk_wall_0 description"
msgid "The maximum instantaneous velocity change with which the outermost walls are printed."
msgstr "Maximální okamžitá změna rychlosti tisku vnějších stěn."

msgctxt "jerk_prime_tower description"
msgid "The maximum instantaneous velocity change with which the prime tower is printed."
msgstr "Maximální okamžitá změna rychlosti, se kterou se tiskne hlavní věž."

msgctxt "jerk_support_interface description"
msgid "The maximum instantaneous velocity change with which the roofs and floors of support are printed."
msgstr "Maximální okamžitá změna rychlosti tisku potisků střech a podlah."

msgctxt "jerk_support_roof description"
msgid "The maximum instantaneous velocity change with which the roofs of support are printed."
msgstr "Maximální okamžitá změna rychlosti, se kterou jsou střechy nosiče vytištěny."

msgctxt "jerk_skirt_brim description"
msgid "The maximum instantaneous velocity change with which the skirt and brim are printed."
msgstr "Maximální okamžitá změna rychlosti, se kterou jsou okraj a límec vytištěny."

msgctxt "jerk_support description"
msgid "The maximum instantaneous velocity change with which the support structure is printed."
msgstr "Maximální okamžitá změna rychlosti, se kterou se tiskne nosná struktura."

msgctxt "jerk_wall_x_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface inner walls are printed."
msgstr "Maximální okamžitá změna rychlosti, jakou jsou tisknuty nejvíce vnější stěny horní plochy."

msgctxt "jerk_wall_0_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface outermost walls are printed."
msgstr "Maximální okamžitá změna rychlosti, jakou jsou tisknuty nejvíce vnitřní stěny horní plochy."

msgctxt "jerk_wall description"
msgid "The maximum instantaneous velocity change with which the walls are printed."
msgstr "Maximální okamžitá změna rychlosti, se kterou se stěny tisknou."

msgctxt "jerk_roofing description"
msgid "The maximum instantaneous velocity change with which top surface skin layers are printed."
msgstr "Maximální okamžitá změna rychlosti, se kterou se potiskují vrchní povrchové vrstvy."

msgctxt "jerk_topbottom description"
msgid "The maximum instantaneous velocity change with which top/bottom layers are printed."
msgstr "Maximální okamžitá změna rychlosti, se kterou se tisknou horní / dolní vrstvy."

msgctxt "jerk_travel description"
msgid "The maximum instantaneous velocity change with which travel moves are made."
msgstr "Maximální okamžitá změna rychlosti, se kterou se pohybují pohyby."

msgctxt "prime_tower_max_bridging_distance description"
msgid "The maximum length of the branches which may be printed over the air."
msgstr ""

msgctxt "machine_max_feedrate_x description"
msgid "The maximum speed for the motor of the X-direction."
msgstr "Maximální rychlost pro motor ve směru X."

msgctxt "machine_max_feedrate_y description"
msgid "The maximum speed for the motor of the Y-direction."
msgstr "Maximální rychlost pro motor ve směru Y."

msgctxt "machine_max_feedrate_z description"
msgid "The maximum speed for the motor of the Z-direction."
msgstr "Maximální rychlost pro motor ve směru Z."

msgctxt "machine_max_feedrate_e description"
msgid "The maximum speed of the filament."
msgstr "Maximální rychlost filamentu."

msgctxt "support_bottom_stair_step_width description"
msgid "The maximum width of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures."
msgstr "Maximální šířka schodů schodišťového dna podpory spočívá na modelu. Nízká hodnota ztěžuje odstranění podpory, ale příliš vysoké hodnoty mohou vést k nestabilním podpůrným strukturám."

msgctxt "mold_width description"
msgid "The minimal distance between the outside of the mold and the outside of the model."
msgstr "Minimální vzdálenost mezi vnější stranou formy a modelu."

msgctxt "machine_minimum_feedrate description"
msgid "The minimal movement speed of the print head."
msgstr "Minimální rychlost pohybu tiskové hlavy."

msgctxt "material_initial_print_temperature description"
msgid "The minimal temperature while heating up to the Printing Temperature at which printing can already start."
msgstr "Minimální teplota při zahřívání až na teplotu tisku, při které již může tisk začít."

msgctxt "machine_min_cool_heat_time_window description"
msgid "The minimal time an extruder has to be inactive before the nozzle is cooled. Only when an extruder is not used for longer than this time will it be allowed to cool down to the standby temperature."
msgstr "Minimální doba, po kterou musí být extrudér neaktivní, než se tryska ochladí. Pouze v případě, že se extrudér nepoužívá déle, než je tato doba, může se ochladit na pohotovostní teplotu."

msgctxt "infill_support_angle description"
msgid "The minimum angle of internal overhangs for which infill is added. At a value of 0° objects are totally filled with infill, 90° will not provide any infill."
msgstr "Minimální úhel vnitřních přesahů, pro které je přidána výplň. Při hodnotě 0 ° jsou objekty zcela vyplněny výplní, 90 ° neposkytuje výplně."

msgctxt "support_angle description"
msgid "The minimum angle of overhangs for which support is added. At a value of 0° all overhangs are supported, 90° will not provide any support."
msgstr "Minimální úhel přesahů, pro které je přidána podpora. Při hodnotě 0° jsou podporovány všechny přesahy, 90° neposkytuje žádnou podporu."

msgctxt "retraction_min_travel description"
msgid "The minimum distance of travel needed for a retraction to happen at all. This helps to get fewer retractions in a small area."
msgstr "Minimální vzdálenost potřebná k tomu, aby ke stažení došlo. To pomáhá dosáhnout menšího počtu stažení v malé oblasti."

msgctxt "skirt_brim_minimal_length description"
msgid "The minimum length of the skirt or brim. If this length is not reached by all skirt or brim lines together, more skirt or brim lines will be added until the minimum length is reached. Note: If the line count is set to 0 this is ignored."
msgstr "Minimální délka límce nebo okraje. Pokud tuto délku nedosáhnou všechny linie okraj nebo límec dohromady, přidává se více okrajových nebo límcových linií, dokud není dosaženo minimální délky. Poznámka: Pokud je počet řádků nastaven na 0, ignoruje se."

msgctxt "min_odd_wall_line_width description"
msgid "The minimum line width for middle line gap filler polyline walls. This setting determines at which model thickness we switch from printing two wall lines, to printing two outer walls and a single central wall in the middle. A higher Minimum Odd Wall Line Width leads to a higher maximum even wall line width. The maximum odd wall line width is calculated as 2 * Minimum Even Wall Line Width."
msgstr "Minimální šířka čáry použité jako výplň mezi párovými čárami zdi. Toto nastavení určuje tloušťku modelu, při které se přepíná z tisku dvou čar zdi na tisk dvou vnějších čar zdi a jedné centrální čáry zdi mezi nimi. Vyšší hodnota Minimální šířky nepárové čáry zdi vede k vyšší maximální šířce párové čáry zdi. Maximální šířka nepárové čáry zdi je vypočtena jako 2 * Minimální šířka párové čáry zdi."

msgctxt "min_even_wall_line_width description"
msgid "The minimum line width for normal polygonal walls. This setting determines at which model thickness we switch from printing a single thin wall line, to printing two wall lines. A higher Minimum Even Wall Line Width leads to a higher maximum odd wall line width. The maximum even wall line width is calculated as Outer Wall Line Width + 0.5 * Minimum Odd Wall Line Width."
msgstr "Minimální šířka čáry pro běžné mnohoúhelníkové zdi. Toto nastavení určuje tloušťku, při které se přepne z tisku jedné tenké čáry zdi na tisk dvou čar zdi. Vyšší hodnota Minimální šířky párové čáry zdi vede k vyšší maximální šířce nepárové čáry zdi. Maximální šířka párové čáry zdi je spočítána jako Šířka čáry vnější stěny + 0,5 * Minimální šířka nepárové čáry zdi."

msgctxt "cool_min_speed description"
msgid "The minimum print speed, despite slowing down due to the minimum layer time. When the printer would slow down too much, the pressure in the nozzle would be too low and result in bad print quality."
msgstr "Minimální rychlost tisku, navzdory zpomalení kvůli minimální době vrstvy. Pokud by tiskárna příliš zpomalila, byl by tlak v trysce příliš nízký a výsledkem by byla špatná kvalita tisku."

msgctxt "meshfix_maximum_resolution description"
msgid "The minimum size of a line segment after slicing. If you increase this, the mesh will have a lower resolution. This may allow the printer to keep up with the speed it has to process g-code and will increase slice speed by removing details of the mesh that it can't process anyway."
msgstr "Minimální velikost segmentu čáry po krájení. Pokud toto zvětšíte, bude mít síť nižší rozlišení. To může umožnit, aby tiskárna udržovala krok s rychlostí, kterou musí zpracovat g-kód, a zvýší se rychlost řezu odstraněním detailů sítě, které stejně nemůže zpracovat."

msgctxt "meshfix_maximum_travel_resolution description"
msgid "The minimum size of a travel line segment after slicing. If you increase this, the travel moves will have less smooth corners. This may allow the printer to keep up with the speed it has to process g-code, but it may cause model avoidance to become less accurate."
msgstr "Minimální velikost segmentu cestovní čáry po krájení. Pokud toto zvýšíte, budou mít cestovní pohyby méně hladké rohy. To může umožnit tiskárně držet krok s rychlostí, kterou musí zpracovat g-kód, ale může to způsobit, že se vyhnutí modelu stane méně přesným."

msgctxt "support_bottom_stair_step_min_slope description"
msgid "The minimum slope of the area for stair-stepping to take effect. Low values should make support easier to remove on shallower slopes, but really low values may result in some very counter-intuitive results on other parts of the model."
msgstr "Minimální sklon oblasti, aby se schodové schody projevily. Nízké hodnoty by měly usnadnit odstraňování podpory na mělkých svazích, ale opravdu nízké hodnoty mohou vést k velmi kontraintuitivním výsledkům na jiných částech modelu."

msgctxt "prime_tower_min_shell_thickness description"
msgid "The minimum thickness of the prime tower shell. You may increase it to make the prime tower stronger."
msgstr ""

msgctxt "cool_min_layer_time description"
msgid "The minimum time spent in a layer. This forces the printer to slow down, to at least spend the time set here in one layer. This allows the printed material to cool down properly before printing the next layer. Layers may still take shorter than the minimal layer time if Lift Head is disabled and if the Minimum Speed would otherwise be violated."
msgstr "Minimální doba strávená ve vrstvě. To nutí tiskárnu zpomalit a alespoň zde strávit čas nastavený v jedné vrstvě. To umožňuje, aby se tištěný materiál před tiskem další vrstvy správně ochladil. Vrstvy mohou stále trvat kratší dobu, než je minimální vrstva, pokud je Lift Head deaktivována a pokud by jinak byla porušena minimální rychlost."

msgctxt "prime_tower_min_volume description"
msgid "The minimum volume for each layer of the prime tower in order to purge enough material."
msgstr "Minimální objem pro každou vrstvu hlavní věže, aby se propláchlo dost materiálu."

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model description"
msgid "The most the diameter of a branch that has to connect to the model may increase by merging with branches that could reach the buildplate. Increasing this reduces print time, but increases the area of support that rests on model"
msgstr "O kolik nejvíce se průměr větve, která se má připojit k modelu, může zvýšit spojením s větvemi, které by se mohly dotýkat podložky. Zvýšení této hodnoty sníží dobu tisku, ale zvýší plochu podpory, která se opírá o model"

msgctxt "machine_name description"
msgid "The name of your 3D printer model."
msgstr "Název vašeho modelu 3D tiskárny."

msgctxt "machine_nozzle_id description"
msgid "The nozzle ID for an extruder train, such as \"AA 0.4\" and \"BB 0.8\"."
msgstr "ID trysky pro vytlačovací stroj, např. \"AA 0.4\" nebo \"BB 0.8\"."

msgctxt "travel_avoid_other_parts description"
msgid "The nozzle avoids already printed parts when traveling. This option is only available when combing is enabled."
msgstr "Při cestování se tryska vyhýbá již potištěným částem. Tato možnost je k dispozici, pouze pokud je povolen combing."

msgctxt "travel_avoid_supports description"
msgid "The nozzle avoids already printed supports when traveling. This option is only available when combing is enabled."
msgstr "Při cestování se tryska vyhýbá již potištěným podpěrám. Tato možnost je k dispozici, pouze pokud je combing povolen."

msgctxt "bottom_layers description"
msgid "The number of bottom layers. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "Počet spodních vrstev. Při výpočtu podle tloušťky dna je tato hodnota zaokrouhlena na celé číslo."

msgctxt "raft_base_wall_count description"
msgid "The number of contours to print around the linear pattern in the base layer of the raft."
msgstr "Kolik obrysových čar se má tisknout okolo lineárního vzoru v základně raftu."

msgctxt "raft_interface_wall_count description"
msgid "The number of contours to print around the linear pattern in the middle layers of the raft."
msgstr ""

msgctxt "raft_surface_wall_count description"
msgid "The number of contours to print around the linear pattern in the top layers of the raft."
msgstr ""

msgctxt "raft_wall_count description"
msgid "The number of contours to print around the linear pattern of the raft."
msgstr ""

msgctxt "skin_edge_support_layers description"
msgid "The number of infill layers that supports skin edges."
msgstr "Počet výplňových vrstev, které podporují okraje povrchu."

msgctxt "initial_bottom_layers description"
msgid "The number of initial bottom layers, from the build-plate upwards. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "Počet počátečních spodních vrstev od montážní desky směrem nahoru. Při výpočtu podle tloušťky dna je tato hodnota zaokrouhlena na celé číslo."

msgctxt "raft_interface_layers description"
msgid "The number of layers between the base and the surface of the raft. These comprise the main thickness of the raft. Increasing this creates a thicker, sturdier raft."
msgstr "Počet vrstev mezi základnou a povrchem raftu. Tyto vrstvy tvoří hlavní část tloušťky raftu. Vyšší hodnota vytvoří tlustší a robustnější raft."

msgctxt "brim_line_count description"
msgid "The number of lines used for a brim. More brim lines enhance adhesion to the build plate, but also reduces the effective print area."
msgstr "Počet řádků použitých pro límec. Více linek límce zvyšuje přilnavost k podložce, ale také snižuje efektivní tiskovou plochu."

msgctxt "support_brim_line_count description"
msgid "The number of lines used for the support brim. More brim lines enhance adhesion to the build plate, at the cost of some extra material."
msgstr "Počet řádků použitých pro podpůrný límec. Více límcových linií zvyšuje přilnavost k stavební desce za cenu nějakého dalšího materiálu."

msgctxt "build_volume_fan_nr description"
msgid "The number of the fan that cools the build volume. If this is set to 0, it's means that there is no build volume fan"
msgstr ""

msgctxt "raft_surface_layers description"
msgid "The number of top layers on top of the 2nd raft layer. These are fully filled layers that the model sits on. 2 layers result in a smoother top surface than 1."
msgstr "Počet vrchních vrstev na druhé vrstvě voru. Jedná se o plně vyplněné vrstvy, na kterých model sedí. Výsledkem 2 vrstev je hladší horní povrch než 1."

msgctxt "top_layers description"
msgid "The number of top layers. When calculated by the top thickness, this value is rounded to a whole number."
msgstr "Počet vrchních vrstev. Při výpočtu podle nejvyšší tloušťky se tato hodnota zaokrouhlí na celé číslo."

msgctxt "roofing_layer_count description"
msgid "The number of top most skin layers. Usually only one top most layer is sufficient to generate higher quality top surfaces."
msgstr "Počet nejpřednějších vrstev pokožky. Obvykle stačí jedna horní vrstva nejvíce k vytvoření horních povrchů vyšší kvality."

msgctxt "support_wall_count description"
msgid "The number of walls with which to surround support infill. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Počet stěn, který mají mít podpory. Přidání stěny může učinit podporu spolehlivější a umožnit podporovat lépe převisy, ale zároveň prodlouží tisk a zvýší spotřebu materiálu."

msgctxt "support_bottom_wall_count description"
msgid "The number of walls with which to surround support interface floor. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Počet stěn, který mají mít podlahy podpor. Přidání stěny může učinit podporu spolehlivější a umožnit podporovat lépe převisy, ale zároveň prodlouží tisk a zvýší spotřebu materiálu."

msgctxt "support_roof_wall_count description"
msgid "The number of walls with which to surround support interface roof. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Počet stěn, který mají mít střechy podpor. Přidání stěny může učinit podporu spolehlivější a umožnit podporovat lépe převisy, ale zároveň prodlouží tisk a zvýší spotřebu materiálu."

msgctxt "support_interface_wall_count description"
msgid "The number of walls with which to surround support interface. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Počet stěn, který mají mít rozhraní podpor. Přidání stěny může učinit podporu spolehlivější a umožnit podporovat lépe převisy, ale zároveň prodlouží tisk a zvýší spotřebu materiálu."

msgctxt "wall_distribution_count description"
msgid "The number of walls, counted from the center, over which the variation needs to be spread. Lower values mean that the outer walls don't change in width."
msgstr "Počet čar zdí od středu, které mají měnit svou šířku při změně počtu čar zdí. Nižší hodnoty znamenají, že vnější stěny nebudou měnit svou šířku."

msgctxt "wall_line_count description"
msgid "The number of walls. When calculated by the wall thickness, this value is rounded to a whole number."
msgstr "Počet stěn. Při výpočtu podle tloušťky stěny je tato hodnota zaokrouhlena na celé číslo."

msgctxt "machine_nozzle_tip_outer_diameter description"
msgid "The outer diameter of the tip of the nozzle."
msgstr "Vnější průměr špičky trysky."

msgctxt "infill_pattern description"
msgid "The pattern of the infill material of the print. The line and zig zag infill swap direction on alternate layers, reducing material cost. The grid, triangle, tri-hexagon, cubic, octet, quarter cubic, cross and concentric patterns are fully printed every layer. Gyroid, cubic, quarter cubic and octet infill change with every layer to provide a more equal distribution of strength over each direction. Lightning infill tries to minimize the infill, by only supporting the ceiling of the object."
msgstr "Vzor výplňového materiálu tisku. Čáry a cik-cak s každou vrstvou obracejí směr výplně, čímž se snižují náklady na materiál. Mřížka, trojúhelník, tri-hexagon, krychle, oktet, čtvrtinově krychlový, křížový a soustředný vzor jsou plně vytištěny v každé vrstvě. Vzory gyroid, krychlový, čtvrtinově krychlový a oktet se mění s každou vrstvou, aby se zajistilo rovnoměrnější rozložení síly v každém směru. Bleskový vzor se snaží minimalizovat množství výplně tím, že podporuje pouze strop objektu."

msgctxt "support_pattern description"
msgid "The pattern of the support structures of the print. The different options available result in sturdy or easy to remove support."
msgstr "Vzor podpůrných struktur tisku. Výsledkem různých dostupných možností je robustní nebo snadno odstranitelná podpora."

msgctxt "roofing_pattern description"
msgid "The pattern of the top most layers."
msgstr "Vzor nejvyšší vrstvy."

msgctxt "top_bottom_pattern description"
msgid "The pattern of the top/bottom layers."
msgstr "Vzor horní / dolní vrstvy."

msgctxt "top_bottom_pattern_0 description"
msgid "The pattern on the bottom of the print on the first layer."
msgstr "Vzor ve spodní části tisku na první vrstvě."

msgctxt "ironing_pattern description"
msgid "The pattern to use for ironing top surfaces."
msgstr "Vzor pro žehlení horních povrchů."

msgctxt "support_bottom_pattern description"
msgid "The pattern with which the floors of the support are printed."
msgstr "Vzor, kterým se potiskují podlahy podpěry."

msgctxt "support_interface_pattern description"
msgid "The pattern with which the interface of the support with the model is printed."
msgstr "Vzor, pomocí kterého je vytištěno rozhraní podpory s modelem."

msgctxt "support_roof_pattern description"
msgid "The pattern with which the roofs of the support are printed."
msgstr "Vzor, kterým se tisknou střechy podpěry."

msgctxt "z_seam_position description"
msgid "The position near where to start printing each part in a layer."
msgstr "Poloha poblíž místa, kde začít tisknout každou část ve vrstvě."

msgctxt "support_tree_angle_slow description"
msgid "The preferred angle of the branches, when they do not have to avoid the model. Use a lower angle to make them more vertical and more stable. Use a higher angle for branches to merge faster."
msgstr "Preferovaný úhel větví, které se nemusí vyhýbat modelu. Použijte nižší úhel, aby byly větve více vertikální a stabilní. Použijte vyšší úhel, aby se větve rychleji spojovaly."

msgctxt "support_tree_rest_preference description"
msgid "The preferred placement of the support structures. If structures can't be placed at the preferred location, they will be place elsewhere, even if that means placing them on the model."
msgstr "Preferované umístění struktur podpory. Pokud nemohou být struktury umístěny na preferované umístění, budou umístěny jinde, i pokud by to mělo znamenat umístění na modelu."

msgctxt "jerk_layer_0 description"
msgid "The print maximum instantaneous velocity change for the initial layer."
msgstr "Maximální okamžitá změna rychlosti tisku pro počáteční vrstvu."

msgctxt "scarf_joint_seam_start_height_ratio description"
msgid "The ratio of the selected layer height at which the scarf seam will begin. A lower number will result in a larger seam height. Must be lower than 100 to be effective."
msgstr ""

msgctxt "machine_shape description"
msgid "The shape of the build plate without taking unprintable areas into account."
msgstr "Tvar desky pro sestavení bez zohlednění netisknutelných oblastí."

msgctxt "machine_head_with_fans_polygon description"
msgid "The shape of the print head. These are coordinates relative to the position of the print head, which is usually the position of its first extruder. The dimensions left and in front of the print head must be negative coordinates."
msgstr "Tvar tiskové hlavy. Toto jsou souřadnice relativně k pozici tiskové hlavy, což je obvykle pozice jejího prvního extruderu. Rozměry vlevo a před tiskovou hlavou musí být negativní souřadnice."

msgctxt "cross_infill_pocket_size description"
msgid "The size of pockets at four-way crossings in the cross 3D pattern at heights where the pattern is touching itself."
msgstr "Velikost kapes na čtyřcestných kříženích v křížovém 3D vzoru ve výškách, kde se vzor sám dotýká."

msgctxt "coasting_min_volume description"
msgid "The smallest volume an extrusion path should have before allowing coasting. For smaller extrusion paths, less pressure has been built up in the bowden tube and so the coasted volume is scaled linearly. This value should always be larger than the Coasting Volume."
msgstr "Nejmenší objem, který by měla mít vytlačovací cesta, než povolí dojezd. U menších vytlačovacích drah se v bowdenové trubici vytvořil menší tlak, a tak se dojezdový objem lineárně upraví. Tato hodnota by měla být vždy větší než dojezdový objem."

msgctxt "machine_nozzle_cool_down_speed description"
msgid "The speed (°C/s) by which the nozzle cools down averaged over the window of normal printing temperatures and the standby temperature."
msgstr "Rychlost (° C / s), kterou tryska ochlazuje, se průměrovala nad oknem normální teploty tisku a pohotovostní teploty."

msgctxt "machine_nozzle_heat_up_speed description"
msgid "The speed (°C/s) by which the nozzle heats up averaged over the window of normal printing temperatures and the standby temperature."
msgstr "Rychlost (° C / s), kterou se tryska zahřívá, se průměruje nad oknem normální teploty tisku a pohotovostní teploty."

msgctxt "speed_wall_x description"
msgid "The speed at which all inner walls are printed. Printing the inner wall faster than the outer wall will reduce printing time. It works well to set this in between the outer wall speed and the infill speed."
msgstr "Rychlost tisku všech vnitřních stěn. Tisk vnitřní stěny rychleji než vnější zeď zkracuje dobu tisku. Funguje dobře, když je nastavena mezi rychlostí vnější stěny a rychlostí výplně."

msgctxt "bridge_skin_speed description"
msgid "The speed at which bridge skin regions are printed."
msgstr "Rychlost, při které se tisknou oblasti povrchu mostu."

msgctxt "speed_infill description"
msgid "The speed at which infill is printed."
msgstr "Rychlost tisku výplně."

msgctxt "speed_print description"
msgid "The speed at which printing happens."
msgstr "Rychlost tisku."

msgctxt "raft_base_speed description"
msgid "The speed at which the base raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "Rychlost tisku základní vrstvy raftu. Toto by se mělo tisknout poměrně pomalu, protože objem materiálu vycházejícího z trysky je poměrně vysoký."

msgctxt "bridge_wall_speed description"
msgid "The speed at which the bridge walls are printed."
msgstr "Rychlost, při které jsou stěny mostu tisknuty."

msgctxt "cool_fan_speed_0 description"
msgid "The speed at which the fans spin at the start of the print. In subsequent layers the fan speed is gradually increased up to the layer corresponding to Regular Fan Speed at Height."
msgstr "Rychlost, kterou se ventilátory otáčejí na začátku tisku. V následujících vrstvách se rychlost ventilátoru postupně zvyšuje až na vrstvu odpovídající normální rychlosti ventilátoru ve výšce."

msgctxt "cool_fan_speed_min description"
msgid "The speed at which the fans spin before hitting the threshold. When a layer prints faster than the threshold, the fan speed gradually inclines towards the maximum fan speed."
msgstr "Rychlost, při které se fanoušci točí před dosažením prahu. Když vrstva tiskne rychleji, než je prahová hodnota, rychlost ventilátoru se postupně naklání směrem k maximální rychlosti ventilátoru."

msgctxt "cool_fan_speed_max description"
msgid "The speed at which the fans spin on the minimum layer time. The fan speed gradually increases between the regular fan speed and maximum fan speed when the threshold is hit."
msgstr "Rychlost, kterou se fanoušci otáčejí při minimální době vrstvy. Rychlost ventilátoru se postupně zvyšuje mezi normální rychlostí ventilátoru a maximální rychlostí ventilátoru, když je dosaženo prahu."

msgctxt "retraction_prime_speed description"
msgid "The speed at which the filament is primed during a retraction move."
msgstr "Rychlost, se kterou se vlákno během navíjení pohybuje."

msgctxt "wipe_retraction_prime_speed description"
msgid "The speed at which the filament is primed during a wipe retraction move."
msgstr "Rychlost, při které je vlákno aktivováno během pohybu stěrače."

msgctxt "switch_extruder_prime_speed description"
msgid "The speed at which the filament is pushed back after a nozzle switch retraction."
msgstr "Rychlost, při které se vlákno tlačí zpět po změně trysky."

msgctxt "retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a retraction move."
msgstr "Rychlost, při které je vlákno zasunuto a aktivováno během pohybu zasunutí."

msgctxt "wipe_retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a wipe retraction move."
msgstr "Rychlost, při které je vlákno zasunuto a aktivováno během pohybu stěrače."

msgctxt "switch_extruder_retraction_speed description"
msgid "The speed at which the filament is retracted during a nozzle switch retract."
msgstr "Rychlost, kterou je vlákno zasunuto během změny trysky."

msgctxt "retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a retraction move."
msgstr "Rychlost, při které se vlákno během zatahovacího pohybu stahuje."

msgctxt "wipe_retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a wipe retraction move."
msgstr "Rychlost, při které je vlákno zataženo během pohybu stěrače."

msgctxt "switch_extruder_retraction_speeds description"
msgid "The speed at which the filament is retracted. A higher retraction speed works better, but a very high retraction speed can lead to filament grinding."
msgstr "Rychlost, při které je vlákno zasunuto. Vyšší retrakční rychlost funguje lépe, ale velmi vysoká retrakční rychlost může vést k broušení vlákna."

msgctxt "speed_support_bottom description"
msgid "The speed at which the floor of support is printed. Printing it at lower speed can improve adhesion of support on top of your model."
msgstr "Rychlost tisku potisku podlahy. Tisk s nižší rychlostí může zlepšit přilnutí podpory na váš model."

msgctxt "speed_support_infill description"
msgid "The speed at which the infill of support is printed. Printing the infill at lower speeds improves stability."
msgstr "Rychlost tisku výplně podpory. Tisk výplně při nižších rychlostech zvyšuje stabilitu."

msgctxt "raft_interface_speed description"
msgid "The speed at which the middle raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "Rychlost tisku střední vrstvy raftu. Toto by se mělo tisknout poměrně pomalu, protože objem materiálu vycházejícího z trysky je poměrně vysoký."

msgctxt "speed_wall_0 description"
msgid "The speed at which the outermost walls are printed. Printing the outer wall at a lower speed improves the final skin quality. However, having a large difference between the inner wall speed and the outer wall speed will affect quality in a negative way."
msgstr "Rychlost tisku vnějších stěn. Tisk vnější stěny nižší rychlostí zlepšuje konečnou kvalitu kůže. Avšak velký rozdíl mezi rychlostí vnitřní stěny a rychlostí vnější stěny negativně ovlivní kvalitu."

msgctxt "speed_prime_tower description"
msgid "The speed at which the prime tower is printed. Printing the prime tower slower can make it more stable when the adhesion between the different filaments is suboptimal."
msgstr "Rychlost tisku hlavní věže. Pomalejší tisk hlavní věže může zvýšit její stabilitu, je-li adheze mezi různými vlákny suboptimální."

msgctxt "cool_fan_speed description"
msgid "The speed at which the print cooling fans spin."
msgstr "Rychlost otáčení ventilátorů chlazení tisku."

msgctxt "raft_speed description"
msgid "The speed at which the raft is printed."
msgstr "Rychlost, při které se raft tiskne."

msgctxt "speed_support_interface description"
msgid "The speed at which the roofs and floors of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "Rychlost, jakou se potiskují střechy a podlahy podpěry. Jejich tisk nižší rychlostí může zlepšit kvalitu převisu."

msgctxt "speed_support_roof description"
msgid "The speed at which the roofs of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "Rychlost, při které jsou střechy podpěry vytištěny. Jejich tisk nižší rychlostí může zlepšit kvalitu převisu."

msgctxt "skirt_brim_speed description"
msgid "The speed at which the skirt and brim are printed. Normally this is done at the initial layer speed, but sometimes you might want to print the skirt or brim at a different speed."
msgstr "Rychlost tisku okraje a límce. Normálně se tak děje při počáteční rychlosti vrstvy, ale někdy můžete chtít okraj nebo límec vytisknout jinou rychlostí."

msgctxt "speed_support description"
msgid "The speed at which the support structure is printed. Printing support at higher speeds can greatly reduce printing time. The surface quality of the support structure is not important since it is removed after printing."
msgstr "Rychlost tisku nosné struktury. Podpora tisku při vyšších rychlostech může výrazně zkrátit dobu tisku. Kvalita povrchu nosné konstrukce není důležitá, protože je odstraněna po tisku."

msgctxt "raft_surface_speed description"
msgid "The speed at which the top raft layers are printed. These should be printed a bit slower, so that the nozzle can slowly smooth out adjacent surface lines."
msgstr "Rychlost tisku horních vrstev raftu. Ty by měly být vytištěny trochu pomaleji, aby tryska mohla pomalu vyhlazovat sousední povrchové linie."

msgctxt "speed_wall_x_roofing description"
msgid "The speed at which the top surface inner walls are printed."
msgstr "Rychlost, kterou jsou tisknuty vnitřní stěny horní plochy."

msgctxt "speed_wall_0_roofing description"
msgid "The speed at which the top surface outermost wall is printed."
msgstr "Rychlost, kterou jsou tisknuty nejvíce vnější stěny horní plochy."

msgctxt "speed_z_hop description"
msgid "The speed at which the vertical Z movement is made for Z Hops. This is typically lower than the print speed since the build plate or machine's gantry is harder to move."
msgstr "Rychlost, při které se svislý pohyb Z provádí pro Z Hopy. To je obvykle nižší než rychlost tisku, protože stavba talíře nebo portálového zařízení je obtížnější se pohybovat."

msgctxt "speed_wall description"
msgid "The speed at which the walls are printed."
msgstr "Rychlost, s jakou se stěny tisknou."

msgctxt "speed_ironing description"
msgid "The speed at which to pass over the top surface."
msgstr "Rychlost, kterou musí projít přes horní povrch."

msgctxt "material_break_speed description"
msgid "The speed at which to retract the filament in order to break it cleanly."
msgstr "Rychlost, kterou se má vlákno navíjet zpět, aby se čistě přerušilo."

msgctxt "speed_roofing description"
msgid "The speed at which top surface skin layers are printed."
msgstr "Rychlost tisku povrchových vrstev povrchu."

msgctxt "speed_topbottom description"
msgid "The speed at which top/bottom layers are printed."
msgstr "Rychlost tisku horní a dolní vrstvy."

msgctxt "speed_travel description"
msgid "The speed at which travel moves are made."
msgstr "Rychlost, jakou se dělají pohyby."

msgctxt "coasting_speed description"
msgid "The speed by which to move during coasting, relative to the speed of the extrusion path. A value slightly under 100% is advised, since during the coasting move the pressure in the bowden tube drops."
msgstr "Rychlost, kterou se má pohybovat během dojezdu, relativně k rychlosti vytlačovací dráhy. Doporučuje se hodnota mírně pod 100%, protože během jízdy se pohybuje tlak v bowdenové trubici."

msgctxt "speed_layer_0 description"
msgid "The speed for the initial layer. A lower value is advised to improve adhesion to the build plate. Does not affect the build plate adhesion structures themselves, like brim and raft."
msgstr "Rychlost počáteční vrstvy. Doporučuje se nižší hodnota pro zlepšení přilnavosti k montážní desce. Nemá vliv na samotné struktury pro přilnavost k podložce (např. límec a raft)."

msgctxt "speed_print_layer_0 description"
msgid "The speed of printing for the initial layer. A lower value is advised to improve adhesion to the build plate."
msgstr "Rychlost tisku pro počáteční vrstvu. Doporučuje se nižší hodnota pro zlepšení přilnavosti k montážní desce."

msgctxt "speed_travel_layer_0 description"
msgid "The speed of travel moves in the initial layer. A lower value is advised to prevent pulling previously printed parts away from the build plate. The value of this setting can automatically be calculated from the ratio between the Travel Speed and the Print Speed."
msgstr "Rychlost pohybu se pohybuje v počáteční vrstvě. Doporučuje se nižší hodnota, aby nedocházelo k tažení dříve potištěných částí pryč od sestavovací desky. Hodnota tohoto nastavení lze automaticky vypočítat z poměru mezi rychlostí cestování a rychlostí tisku."

msgctxt "material_break_temperature description"
msgid "The temperature at which the filament is broken for a clean break."
msgstr "Teplota, při které je filament možno přerušit pro čisté přerušení."

msgctxt "build_volume_temperature description"
msgid "The temperature of the environment to print in. If this is 0, the build volume temperature will not be adjusted."
msgstr "Teplota prostředí, ve kterém se má tisknout. Pokud je to 0, nebude se brát teplota prostředí v potaz."

msgctxt "material_standby_temperature description"
msgid "The temperature of the nozzle when another nozzle is currently used for printing."
msgstr "Teplota trysky, když je pro tisk aktuálně použita jiná tryska."

msgctxt "material_final_print_temperature description"
msgid "The temperature to which to already start cooling down just before the end of printing."
msgstr "Teplota, na kterou se má začít ochlazovat těsně před koncem tisku."

msgctxt "material_print_temperature_layer_0 description"
msgid "The temperature used for printing the first layer."
msgstr ""

msgctxt "material_print_temperature description"
msgid "The temperature used for printing."
msgstr "Teplota, která se používá pro tisk."

msgctxt "material_bed_temperature_layer_0 description"
msgid "The temperature used for the heated build plate at the first layer. If this is 0, the build plate is left unheated during the first layer."
msgstr "Teplota použitá pro vyhřívanou podložku při první vrstvě. Pokud je to 0, podložka se při první vrstvě vyhřívat nebude."

msgctxt "material_bed_temperature description"
msgid "The temperature used for the heated build plate. If this is 0, the build plate is left unheated."
msgstr "Teplota použitá pro vyhřívanou podložku. Pokud je to 0, podložka se vyhřívat nebude."

msgctxt "material_break_preparation_temperature description"
msgid "The temperature used to purge material, should be roughly equal to the highest possible printing temperature."
msgstr "Teplota použitá k čištění materiálu by měla být zhruba stejná jako nejvyšší možná teplota tisku."

msgctxt "bottom_thickness description"
msgid "The thickness of the bottom layers in the print. This value divided by the layer height defines the number of bottom layers."
msgstr "Tloušťka spodních vrstev v tisku. Tato hodnota dělená výškou vrstvy definuje počet spodních vrstev."

msgctxt "skin_edge_support_thickness description"
msgid "The thickness of the extra infill that supports skin edges."
msgstr "Tloušťka další výplně, která podporuje okraje povrchu."

msgctxt "support_interface_height description"
msgid "The thickness of the interface of the support where it touches with the model on the bottom or the top."
msgstr "Tloušťka rozhraní podpěry, kde se dotýká modelu na spodní nebo horní straně."

msgctxt "support_bottom_height description"
msgid "The thickness of the support floors. This controls the number of dense layers that are printed on top of places of a model on which support rests."
msgstr "Tloušťka nosných podlah. Tím se řídí počet hustých vrstev, které jsou vytištěny na místech modelu, na kterých leží podpora."

msgctxt "support_roof_height description"
msgid "The thickness of the support roofs. This controls the amount of dense layers at the top of the support on which the model rests."
msgstr "Tloušťka nosných střech. Tím se řídí množství hustých vrstev v horní části nosiče, na kterém model spočívá."

msgctxt "top_thickness description"
msgid "The thickness of the top layers in the print. This value divided by the layer height defines the number of top layers."
msgstr "Tloušťka horních vrstev v tisku. Tato hodnota dělená výškou vrstvy definuje počet vrchních vrstev."

msgctxt "top_bottom_thickness description"
msgid "The thickness of the top/bottom layers in the print. This value divided by the layer height defines the number of top/bottom layers."
msgstr "Tloušťka horní / dolní vrstvy v tisku. Tato hodnota dělená výškou vrstvy definuje počet vrstev horní / dolní."

msgctxt "wall_thickness description"
msgid "The thickness of the walls in the horizontal direction. This value divided by the wall line width defines the number of walls."
msgstr "Tloušťka stěn v horizontálním směru. Tato hodnota dělená šířkou čáry stěny definuje počet stěn."

msgctxt "infill_sparse_thickness description"
msgid "The thickness per layer of infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "Tloušťka výplňového materiálu na vrstvu. Tato hodnota by měla být vždy násobkem výšky vrstvy a je jinak zaoblená."

msgctxt "support_infill_sparse_thickness description"
msgid "The thickness per layer of support infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "Tloušťka na vrstvu nosného výplňového materiálu. Tato hodnota by měla být vždy násobkem výšky vrstvy a je jinak zaoblená."

msgctxt "machine_gcode_flavor description"
msgid "The type of g-code to be generated."
msgstr "Typ generovaného g-kódu."

msgctxt "material_type description"
msgid "The type of material used."
msgstr ""

msgctxt "coasting_volume description"
msgid "The volume otherwise oozed. This value should generally be close to the nozzle diameter cubed."
msgstr "Objem jinak vytekl. Tato hodnota by měla být obecně blízká průměru trysek."

msgctxt "machine_width description"
msgid "The width (X-direction) of the printable area."
msgstr "Šířka (Osa X) plochy k tisku."

msgctxt "support_brim_width description"
msgid "The width of the brim to print underneath the support. A larger brim enhances adhesion to the build plate, at the cost of some extra material."
msgstr "Šířka okraje pro tisk pod podpěrou. Větší okraj zvyšuje přilnavost ke podložce za cenu nějakého dalšího materiálu."

msgctxt "interlocking_beam_width description"
msgid "The width of the interlocking structure beams."
msgstr "Šířka paprsků vzájemného propletení."

msgctxt "prime_tower_base_size description"
msgid "The width of the prime tower brim/base. A larger base enhances adhesion to the build plate, but also reduces the effective print area."
msgstr ""

msgctxt "prime_tower_size description"
msgid "The width of the prime tower."
msgstr "Šířka hlavní věže."

msgctxt "magic_fuzzy_skin_thickness description"
msgid "The width within which to jitter. It's advised to keep this below the outer wall width, since the inner walls are unaltered."
msgstr "Šířka, do které se chvěje. Doporučuje se to udržovat pod šířkou vnější stěny, protože vnitřní stěny zůstávají nezměněny."

msgctxt "retraction_extrusion_window description"
msgid "The window in which the maximum retraction count is enforced. This value should be approximately the same as the retraction distance, so that effectively the number of times a retraction passes the same patch of material is limited."
msgstr "Okno, ve kterém je vynucován maximální počet stažení. Tato hodnota by měla být přibližně stejná jako retrakční vzdálenost, takže je účinně omezen počet opakování protažení stejnou vrstvou materiálu."

msgctxt "prime_tower_position_x description"
msgid "The x coordinate of the position of the prime tower."
msgstr "Souřadnice X polohy hlavní věže."

msgctxt "prime_tower_position_y description"
msgid "The y coordinate of the position of the prime tower."
msgstr "Souřadnice Y polohy hlavní věže."

msgctxt "support_meshes_present description"
msgid "There are support meshes present in the scene. This setting is controlled by Cura."
msgstr "Ve scéně existují podpůrné masky. Toto nastavení je kontrolováno Curou."

msgctxt "bridge_wall_coast description"
msgid "This controls the distance the extruder should coast immediately before a bridge wall begins. Coasting before the bridge starts can reduce the pressure in the nozzle and may produce a flatter bridge."
msgstr "Tím se řídí vzdálenost, kterou by extrudér měl dojet bezprostředně před začátkem zdi mostu. Pobyt před startem můstku může snížit tlak v trysce a může vést k ploššímu můstku."

msgctxt "wall_0_acceleration description"
msgid "This is the acceleration with which to reach the top speed when printing an outer wall."
msgstr ""

msgctxt "wall_0_deceleration description"
msgid "This is the deceleration with which to end printing an outer wall."
msgstr ""

msgctxt "wall_0_speed_split_distance description"
msgid "This is the maximum length of an extrusion path when splitting a longer path to apply the outer wall acceleration/deceleration. A smaller distance will create a more precise but also more verbose G-Code."
msgstr ""

msgctxt "wall_0_end_speed_ratio description"
msgid "This is the ratio of the top speed to end with when printing an outer wall."
msgstr ""

msgctxt "wall_0_start_speed_ratio description"
msgid "This is the ratio of the top speed to start with when printing an outer wall."
msgstr ""

msgctxt "raft_base_smoothing description"
msgid "This setting controls how much inner corners in the raft base outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr ""

msgctxt "raft_interface_smoothing description"
msgid "This setting controls how much inner corners in the raft middle outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr ""

msgctxt "raft_smoothing description"
msgid "This setting controls how much inner corners in the raft outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "Toto nastavení řídí, kolik vnitřních rohů v obrysu raftů je zaobleno. Vnitřní rohy jsou zaokrouhleny na půlkruh s poloměrem rovným zde uvedené hodnotě. Toto nastavení také odstraní otvory v obrysu raftu, které jsou menší než takový kruh."

msgctxt "raft_surface_smoothing description"
msgid "This setting controls how much inner corners in the raft top outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr ""

msgctxt "retraction_count_max description"
msgid "This setting limits the number of retractions occurring within the minimum extrusion distance window. Further retractions within this window will be ignored. This avoids retracting repeatedly on the same piece of filament, as that can flatten the filament and cause grinding issues."
msgstr "Toto nastavení omezuje počet stažení, ke kterým dochází v okně minimální vzdálenosti vytlačování. Další stažení v tomto okně budou ignorovány. Tím se zabrání opakovanému navíjení na stejný kus vlákna, protože to může vlákno zploštit a způsobit problémy s broušením."

msgctxt "draft_shield_enabled description"
msgid "This will create a wall around the model, which traps (hot) air and shields against exterior airflow. Especially useful for materials which warp easily."
msgstr "Tím se vytvoří kolem modelu zeď, která zachycuje (horký) vzduch a chrání před vnějším proudem vzduchu. Obzvláště užitečné pro materiály, které se snadno deformují."

msgctxt "support_tree_tip_diameter label"
msgid "Tip Diameter"
msgstr "Průměr konečků"

msgctxt "material_shrinkage_percentage_xy description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the XY-direction (horizontally)."
msgstr "Model bude zvětšen tímto faktorem ve směru XY (horizontálně), aby bylo kompenzováno smrštění materiálu po vychladnutí."

msgctxt "material_shrinkage_percentage_z description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the Z-direction (vertically)."
msgstr "Model bude zvětšen tímto faktorem ve směru Z (vertikálně), aby bylo kompenzováno smrštění materiálu po vychladnutí."

msgctxt "material_shrinkage_percentage description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor."
msgstr "Model bude zvětšen tímto faktorem, aby bylo kompenzováno smrštění materiálu po vychladnutí."

msgctxt "top_layers label"
msgid "Top Layers"
msgstr "Vrchní vrstvy"

msgctxt "top_skin_expand_distance label"
msgid "Top Skin Expand Distance"
msgstr "Horní vzdálenost rozšíření povrchu"

msgctxt "top_skin_preshrink label"
msgid "Top Skin Removal Width"
msgstr "Horní šířka odstranění povrchu"

msgctxt "acceleration_wall_x_roofing label"
msgid "Top Surface Inner Wall Acceleration"
msgstr "Zrychlení vnitřní stěny horní plochy"

msgctxt "jerk_wall_x_roofing label"
msgid "Top Surface Inner Wall Jerk"
msgstr "Rychlá změna nejvíce vnější stěny horní plochy"

msgctxt "speed_wall_x_roofing label"
msgid "Top Surface Inner Wall Speed"
msgstr "Rychlost vnitřní stěny horní plochy"

msgctxt "wall_x_material_flow_roofing label"
msgid "Top Surface Inner Wall(s) Flow"
msgstr "Tok vnitřní stěny horní plochy"

msgctxt "acceleration_wall_0_roofing label"
msgid "Top Surface Outer Wall Acceleration"
msgstr "Zrychlení vnější stěny horní plochy"

msgctxt "wall_0_material_flow_roofing label"
msgid "Top Surface Outer Wall Flow"
msgstr "Tok nejvíce vnější stěnové linky horní plochy"

msgctxt "jerk_wall_0_roofing label"
msgid "Top Surface Outer Wall Jerk"
msgstr "Rychlá změna nejvíce vnitřní stěny horní plochy"

msgctxt "speed_wall_0_roofing label"
msgid "Top Surface Outer Wall Speed"
msgstr "Rychlost nejvíce vnější stěny horní plochy"

msgctxt "acceleration_roofing label"
msgid "Top Surface Skin Acceleration"
msgstr "Akcelerace tisku horního povrchu"

msgctxt "roofing_extruder_nr label"
msgid "Top Surface Skin Extruder"
msgstr "Nejvyšší povrchový extrudér"

msgctxt "roofing_material_flow label"
msgid "Top Surface Skin Flow"
msgstr "Nejlepší horní povrchový tok"

msgctxt "jerk_roofing label"
msgid "Top Surface Skin Jerk"
msgstr "Okamžitá rychlost při tisku horního povrchu"

msgctxt "roofing_layer_count label"
msgid "Top Surface Skin Layers"
msgstr "Nejvyšší povrchová vrstva"

msgctxt "roofing_angles label"
msgid "Top Surface Skin Line Directions"
msgstr "Pokyny pro horní povrchovou linii"

msgctxt "roofing_line_width label"
msgid "Top Surface Skin Line Width"
msgstr "Nejvyšší šířka linie povrchu"

msgctxt "roofing_pattern label"
msgid "Top Surface Skin Pattern"
msgstr "Vzor horního povrchu"

msgctxt "speed_roofing label"
msgid "Top Surface Skin Speed"
msgstr "Rychlost tisku horního povrchu"

msgctxt "top_thickness label"
msgid "Top Thickness"
msgstr "Vrchní tloušťka"

msgctxt "max_skin_angle_for_expansion description"
msgid "Top and/or bottom surfaces of your object with an angle larger than this setting, won't have their top/bottom skin expanded. This avoids expanding the narrow skin areas that are created when the model surface has a near vertical slope. An angle of 0° is horizontal and will cause no skin to be expanded, while an angle of 90° is vertical and will cause all skin to be expanded."
msgstr "Horní a/nebo dolní povrchy objektu s větším úhlem, než je toto nastavení, nebudou mít své horní/spodní povrchy rozšířeny. Tím se zabrání rozšíření úzkých oblastí, které jsou vytvořeny, když má povrch modelu téměř svislý sklon. Úhel 0° je vodorovný a způsobí, že žádný povrch nebude rozšířen, zatímco úhel 90° je svislý a způsobí, že všechny povrchy budou rozšířeny."

msgctxt "top_bottom description"
msgid "Top/Bottom"
msgstr "Vrch/spodek"

msgctxt "top_bottom label"
msgid "Top/Bottom"
msgstr "Vrch/spodek"

msgctxt "acceleration_topbottom label"
msgid "Top/Bottom Acceleration"
msgstr "Akcelerace tisku nahoře/dole"

msgctxt "top_bottom_extruder_nr label"
msgid "Top/Bottom Extruder"
msgstr "Vrchní/spodní extruder"

msgctxt "skin_material_flow label"
msgid "Top/Bottom Flow"
msgstr "Horní/spodní průtok"

msgctxt "jerk_topbottom label"
msgid "Top/Bottom Jerk"
msgstr "Okamžitá rychlost při tisku vršku/spodku"

msgctxt "skin_angles label"
msgid "Top/Bottom Line Directions"
msgstr "Pokyny pro horní a dolní řádek"

msgctxt "skin_line_width label"
msgid "Top/Bottom Line Width"
msgstr "Horní/dolní šířka čáry"

msgctxt "top_bottom_pattern label"
msgid "Top/Bottom Pattern"
msgstr "Vrchní/spodní vzor"

msgctxt "speed_topbottom label"
msgid "Top/Bottom Speed"
msgstr "Rychlost tisku horní/spodní vrstvy"

msgctxt "top_bottom_thickness label"
msgid "Top/Bottom Thickness"
msgstr "Vrchní/spodní tloušťka"

msgctxt "support_type option buildplate"
msgid "Touching Buildplate"
msgstr "Dotýká se podložky"

msgctxt "support_tower_diameter label"
msgid "Tower Diameter"
msgstr "Průměr věže"

msgctxt "support_tower_roof_angle label"
msgid "Tower Roof Angle"
msgstr "Úhel střechy věže"

msgctxt "mesh_rotation_matrix description"
msgid "Transformation matrix to be applied to the model when loading it from file."
msgstr "Transformační matice, která se použije na model při načítání ze souboru."

msgctxt "travel label"
msgid "Travel"
msgstr "Pohyb"

msgctxt "acceleration_travel label"
msgid "Travel Acceleration"
msgstr "Cestovní akcelerace"

msgctxt "travel_avoid_distance label"
msgid "Travel Avoid Distance"
msgstr "Vzdálenost vyhnutí se při pohybu"

msgctxt "jerk_travel label"
msgid "Travel Jerk"
msgstr "Okamžitá rychlost při cestování"

msgctxt "speed_travel label"
msgid "Travel Speed"
msgstr "Cestovní rychlost"

msgctxt "magic_mesh_surface_mode description"
msgid "Treat the model as a surface only, a volume, or volumes with loose surfaces. The normal print mode only prints enclosed volumes. \"Surface\" prints a single wall tracing the mesh surface with no infill and no top/bottom skin. \"Both\" prints enclosed volumes like normal and any remaining polygons as surfaces."
msgstr "S modelem zacházejte pouze jako s povrchem, objemem nebo objemy s volnými povrchy. Normální režim tisku tiskne pouze uzavřené svazky. „Povrch“ vytiskne jedinou stěnu, která sleduje povrch oka bez výplně a bez horní / dolní povrch. „Oba“ tiskne uzavřené svazky jako normální a všechny zbývající polygony jako povrchy."

msgctxt "support_structure option tree"
msgid "Tree"
msgstr "Strom"

msgctxt "infill_pattern option trihexagon"
msgid "Tri-Hexagon"
msgstr "Tri-Hexagony"

msgctxt "infill_pattern option triangles"
msgid "Triangles"
msgstr "Trojúhelníky"

msgctxt "support_bottom_pattern option triangles"
msgid "Triangles"
msgstr "Trojúhelníky"

msgctxt "support_interface_pattern option triangles"
msgid "Triangles"
msgstr "Trojúhelníky"

msgctxt "support_pattern option triangles"
msgid "Triangles"
msgstr "Trojúhelníky"

msgctxt "support_roof_pattern option triangles"
msgid "Triangles"
msgstr "Trojúhelníky"

msgctxt "support_tree_max_diameter label"
msgid "Trunk Diameter"
msgstr "Průměr kmene"

msgctxt "seam_overhang_angle description"
msgid "Try to prevent seams on walls that overhang more than this angle. When the value is 90, no walls will be treated as overhanging."
msgstr ""

msgctxt "machine_gcode_flavor option UltiGCode"
msgid "Ultimaker 2"
msgstr "Ultimaker 2"

msgctxt "cool_during_extruder_switch option unchanged"
msgid "Unchanged"
msgstr ""

msgctxt "meshfix_union_all label"
msgid "Union Overlapping Volumes"
msgstr "Spojit překrývající se objekty"

msgctxt "bridge_wall_min_length description"
msgid "Unsupported walls shorter than this will be printed using the normal wall settings. Longer unsupported walls will be printed using the bridge wall settings."
msgstr "Nepodporované stěny kratší než tato budou vytištěny pomocí běžného nastavení zdi. Delší nepodporované stěny budou vytištěny pomocí nastavení mostní zdi."

msgctxt "adaptive_layer_height_enabled label"
msgid "Use Adaptive Layers"
msgstr "Použít adaptivní vrstvy"

msgctxt "support_use_towers label"
msgid "Use Towers"
msgstr "Používat věže"

msgctxt "acceleration_travel_enabled description"
msgid "Use a separate acceleration rate for travel moves. If disabled, travel moves will use the acceleration value of the printed line at their destination."
msgstr "Použít jiné samostatné akcelerace pro cestovní pohyby. Pokud je vypnuto, pak budou cestovní pohyby používat hodnotu akcelerace podle tištěné čáry v cíli pohybu."

msgctxt "jerk_travel_enabled description"
msgid "Use a separate jerk rate for travel moves. If disabled, travel moves will use the jerk value of the printed line at their destination."
msgstr "Použít samostatné nastavení okamžité rychlosti pro cestovní pohyby. Pokud je vypnuto, pak budou cestovní pohyby používat hodnotu okamžité rychlosti podle tištěné čáry v cíli pohybu."

msgctxt "relative_extrusion description"
msgid "Use relative extrusion rather than absolute extrusion. Using relative E-steps makes for easier post-processing of the g-code. However, it's not supported by all printers and it may produce very slight deviations in the amount of deposited material compared to absolute E-steps. Irrespective of this setting, the extrusion mode will always be set to absolute before any g-code script is output."
msgstr "Použijte spíše relativní extruzi než absolutní extruzi. Použití relativních E-kroků usnadňuje následné zpracování g-kódu. Není však podporována všemi tiskárnami a může vést k velmi malým odchylkám v množství ukládaného materiálu ve srovnání s absolutními kroky E. Bez ohledu na toto nastavení bude režim vytlačování vždy nastaven na absolutní před výstupem jakéhokoli skriptu g-kódu."

msgctxt "support_use_towers description"
msgid "Use specialized towers to support tiny overhang areas. These towers have a larger diameter than the region they support. Near the overhang the towers' diameter decreases, forming a roof."
msgstr "K podpoře malých převislých oblastí použijte specializované věže. Tyto věže mají větší průměr než oblast, kterou podporují. V blízkosti převisu se průměr věží zmenšuje a vytváří střechu."

msgctxt "infill_mesh description"
msgid "Use this mesh to modify the infill of other meshes with which it overlaps. Replaces infill regions of other meshes with regions for this mesh. It's suggested to only print one Wall and no Top/Bottom Skin for this mesh."
msgstr "Pomocí této mřížky můžete upravit výplň dalších sítí, s nimiž se překrývá. Nahrazuje výplňové oblasti jiných sítí oblastmi pro tuto síť. Pro tuto mřížku se doporučuje tisknout pouze jednu zeď a žádnou horní / dolní vrstvu."

msgctxt "support_mesh description"
msgid "Use this mesh to specify support areas. This can be used to generate support structure."
msgstr "Pomocí této sítě můžete určit oblasti podpory. To lze použít ke generování podpůrné struktury."

msgctxt "anti_overhang_mesh description"
msgid "Use this mesh to specify where no part of the model should be detected as overhang. This can be used to remove unwanted support structure."
msgstr "Pomocí této mřížky určete, kde by žádná část modelu neměla být detekována jako převis. To lze použít k odstranění nežádoucí podpůrné struktury."

msgctxt "z_seam_type option back"
msgid "User Specified"
msgstr "Uživatelem specifikováno"

msgctxt "material_shrinkage_percentage_z label"
msgid "Vertical Scaling Factor Shrinkage Compensation"
msgstr "Vertikální faktor zvětšení pro kompenzaci smrštění"

msgctxt "slicing_tolerance description"
msgid "Vertical tolerance in the sliced layers. The contours of a layer are normally generated by taking cross sections through the middle of each layer's thickness (Middle). Alternatively each layer can have the areas which fall inside of the volume throughout the entire thickness of the layer (Exclusive) or a layer has the areas which fall inside anywhere within the layer (Inclusive). Inclusive retains the most details, Exclusive makes for the best fit and Middle stays closest to the original surface."
msgstr "Vertikální tolerance ve slicovaných vrstvách. Obrysy vrstvy jsou obvykle vytvářeny průřezy středem tloušťky každé vrstvy (uprostřed). Alternativně každá vrstva může mít oblasti, které spadají dovnitř objemu po celé tloušťce vrstvy (Exkluzivní), nebo vrstva má oblasti, které padají dovnitř kdekoli v rámci vrstvy (Inkluzivní). Inclusive si zachovává nejpodrobnější detaily, Exclusive dělá to nejlepší a Middle zůstává co nejblíže původnímu povrchu."

msgctxt "material_bed_temp_wait label"
msgid "Wait for Build Plate Heatup"
msgstr "Čekat na zahřátí desky"

msgctxt "material_print_temp_wait label"
msgid "Wait for Nozzle Heatup"
msgstr "Čekat na zahřátí trysek"

msgctxt "acceleration_wall label"
msgid "Wall Acceleration"
msgstr "Akcelerace tisku zdi"

msgctxt "wall_distribution_count label"
msgid "Wall Distribution Count"
msgstr "Počet čar zdí měnících šířku"

msgctxt "wall_extruder_nr label"
msgid "Wall Extruder"
msgstr "Extruder zdi"

msgctxt "wall_material_flow label"
msgid "Wall Flow"
msgstr "Průtok u zdi"

msgctxt "jerk_wall label"
msgid "Wall Jerk"
msgstr "Okamžitá rychlost při tisku zdi"

msgctxt "wall_line_count label"
msgid "Wall Line Count"
msgstr "Počet čar zdi"

msgctxt "wall_line_width label"
msgid "Wall Line Width"
msgstr "Šířka čáry stěny"

msgctxt "inset_direction label"
msgid "Wall Ordering"
msgstr "Pořadí tisku zdí"

msgctxt "speed_wall label"
msgid "Wall Speed"
msgstr "Rychlost tisku zdi"

msgctxt "wall_thickness label"
msgid "Wall Thickness"
msgstr "Tloušťka stěny"

msgctxt "wall_transition_length label"
msgid "Wall Transition Length"
msgstr "Délka změny počtu čar zdí"

msgctxt "wall_transition_filter_distance label"
msgid "Wall Transitioning Filter Distance"
msgstr "Minimální vzdálenost změny počtu čar zdí"

msgctxt "wall_transition_filter_deviation label"
msgid "Wall Transitioning Filter Margin"
msgstr "Rezerva pro změnu počtu čar zdí"

msgctxt "wall_transition_angle label"
msgid "Wall Transitioning Threshold Angle"
msgstr "Prahový úhel pro změnu počtu čar zdí"

msgctxt "shell label"
msgid "Walls"
msgstr "Stěny"

msgctxt "extra_infill_lines_to_support_skins option walls"
msgid "Walls Only"
msgstr ""

msgctxt "extra_infill_lines_to_support_skins option walls_and_lines"
msgid "Walls and Lines"
msgstr ""

msgctxt "wall_overhang_angle description"
msgid "Walls that overhang more than this angle will be printed using overhanging wall settings. When the value is 90, no walls will be treated as overhanging. Overhang that gets supported by support will not be treated as overhang either. Furthermore, any line that's less than half overhanging will also not be treated as overhang."
msgstr ""

msgctxt "meshfix_fluid_motion_enabled description"
msgid "When enabled tool paths are corrected for printers with smooth motion planners. Small movements that deviate from the general tool path direction are smoothed to improve fluid motions."
msgstr ""

msgctxt "infill_enable_travel_optimization description"
msgid "When enabled, the order in which the infill lines are printed is optimized to reduce the distance travelled. The reduction in travel time achieved very much depends on the model being sliced, infill pattern, density, etc. Note that, for some models that have many small areas of infill, the time to slice the model may be greatly increased."
msgstr "Je-li tato funkce povolena, je pořadí, ve kterém jsou vyplněny řádky výplně, optimalizováno, aby se snížila ujetá vzdálenost. Zkrácení doby cestování dosažené velmi záleží na modelu, který je nakrájen, vzor výplně, hustota atd. U některých modelů, které mají mnoho malých oblastí výplně, může být doba krájení modelu značně prodloužena."

msgctxt "support_fan_enable description"
msgid "When enabled, the print cooling fan speed is altered for the skin regions immediately above the support."
msgstr "Je-li tato funkce povolena, mění se rychlost ventilátoru chlazení tisku pro oblasti kůže bezprostředně nad podporou."

msgctxt "z_seam_relative description"
msgid "When enabled, the z seam coordinates are relative to each part's centre. When disabled, the coordinates define an absolute position on the build plate."
msgstr "Pokud je tato možnost povolena, jsou souřadnice z švu vztaženy ke středu každé součásti. Pokud je zakázána, souřadnice definují absolutní polohu na sestavovací desce."

msgctxt "retraction_combing_max_distance description"
msgid "When greater than zero, combing travel moves that are longer than this distance will use retraction. If set to zero, there is no maximum and combing moves will not use retraction."
msgstr "Pokud je větší než nula, combingové pohyby delší než tato vzdálenost budou používat retrakci. Nula znamená, že se při combingových pohybech retrakce provádět nebudou."

msgctxt "hole_xy_offset_max_diameter description"
msgid "When greater than zero, the Hole Horizontal Expansion is gradually applied on small holes (small holes are expanded more). When set to zero the Hole Horizontal Expansion will be applied to all holes. Holes larger than the Hole Horizontal Expansion Max Diameter are not expanded."
msgstr "Když je větší než nula, Horizontální expanze díry je stupňovitě aplikována na malé díry (malé díry jsou zvětšovány více). Pokud je nastavení nula, Horizontální expanze díry bude aplikována na všechny díry. Díry větší, než Maximální průměr horizontální expanze díry nebudou zvětšeny."

msgctxt "hole_xy_offset description"
msgid "When greater than zero, the Hole Horizontal Expansion is the amount of offset applied to all holes in each layer. Positive values increase the size of the holes, negative values reduce the size of the holes. When this setting is enabled it can be further tuned with Hole Horizontal Expansion Max Diameter."
msgstr ""

msgctxt "bridge_skin_material_flow description"
msgid "When printing bridge skin regions, the amount of material extruded is multiplied by this value."
msgstr "Při tisku oblastí povrchu můstku je množství vytlačovaného materiálu násobeno touto hodnotou."

msgctxt "bridge_wall_material_flow description"
msgid "When printing bridge walls, the amount of material extruded is multiplied by this value."
msgstr "Při tisku stěn můstku je množství vytlačovaného materiálu násobeno touto hodnotou."

msgctxt "raft_interface_z_offset description"
msgid "When printing the first layer of the raft interface, translate by this offset to customize the adhesion between base and interface. A negative offset should improve the adhesion."
msgstr ""

msgctxt "raft_surface_z_offset description"
msgid "When printing the first layer of the raft surface, translate by this offset to customize the adhesion between interface and surface. A negative offset should improve the adhesion."
msgstr ""

msgctxt "bridge_skin_material_flow_2 description"
msgid "When printing the second bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "Při tisku druhé vrstvy potahu se množství vytlačovaného materiálu násobí touto hodnotou."

msgctxt "bridge_skin_material_flow_3 description"
msgid "When printing the third bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "Při tisku třetí vrstvy potahu se množství vytlačovaného materiálu násobí touto hodnotou."

msgctxt "cool_lift_head description"
msgid "When the minimum speed is hit because of minimum layer time, lift the head away from the print and wait the extra time until the minimum layer time is reached."
msgstr "Pokud je minimální rychlost zasažena z důvodu minimálního času vrstvy, zvedněte hlavu z tisku a vyčkejte další čas, dokud není dosaženo minimálního času vrstvy."

msgctxt "skin_no_small_gaps_heuristic description"
msgid "When the model has small vertical gaps of only a few layers, there should normally be skin around those layers in the narrow space. Enable this setting to not generate skin if the vertical gap is very small. This improves printing time and slicing time, but technically leaves infill exposed to the air."
msgstr "Pokud má model malé svislé mezery pouze v několika vrstvách, měla by být kolem těchto vrstev v úzkém prostoru normální povrch. Povolte toto nastavení, abyste nevytvořili vzhled, pokud je vertikální mezera velmi malá. To zlepšuje dobu tisku a slicování, ale technicky zůstává výplň vystavena vzduchu."

msgctxt "wall_transition_angle description"
msgid "When to create transitions between even and odd numbers of walls. A wedge shape with an angle greater than this setting will not have transitions and no walls will be printed in the center to fill the remaining space. Reducing this setting reduces the number and length of these center walls, but may leave gaps or overextrude."
msgstr "Určuje, kdy se bude měnit počet čar zdí. Pokud se v modelu vyskytuje tvar klínu s úhlem přesahujícím hodnotu tohoto nastavení, nebudou se pro vyplnění prostoru uprostřed používat sbíhající se čáry zdí s proměnlivou šířkou. Snížením této hodnoty se snižuje počet a délka těchto prostředních zdí, ale může zůstávat více mezer nebo nadměrná extruze."

msgctxt "wall_transition_length description"
msgid "When transitioning between different numbers of walls as the part becomes thinner, a certain amount of space is allotted to split or join the wall lines."
msgstr "Když dochází k přechodu mezi různými počty čar zdí v místě, kde se část stává užší, je určitý prostor vyhrazen pro rozdělení nebo spojení čar zdí."

msgctxt "wipe_hop_enable description"
msgid "When wiping, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "Při stírání se podložka spustí, aby se vytvořila vůle mezi tryskou a tiskem. Zabraňuje tomu, aby tryska narazila na tisk během pohybů, což snižuje šanci vyrazit tisk z montážní desky."

msgctxt "retraction_hop_enabled description"
msgid "Whenever a retraction is done, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "Kdykoli je provedeno zasunutí, sestavovací deska se spustí, aby se vytvořila vůle mezi tryskou a tiskem. Zabraňuje tomu, aby tryska narazila na tisk během pohybů, což snižuje šanci vyrazit tisk z montážní desky."

msgctxt "support_xy_overrides_z description"
msgid "Whether the Support X/Y Distance overrides the Support Z Distance or vice versa. When X/Y overrides Z the X/Y distance can push away the support from the model, influencing the actual Z distance to the overhang. We can disable this by not applying the X/Y distance around overhangs."
msgstr "Zda podpůrná vzdálenost X / Y přepíše podpůrnou vzdálenost Z nebo naopak. Když X / Y přepíše Z, X / Y vzdálenost může vytlačit podporu z modelu, což ovlivňuje skutečnou Z vzdálenost k převisu. Můžeme to zakázat tím, že nepoužijeme vzdálenost X / Y kolem převisů."

msgctxt "machine_center_is_zero description"
msgid "Whether the X/Y coordinates of the zero position of the printer is at the center of the printable area."
msgstr "Zda jsou souřadnice X / Y nulové polohy tiskárny ve středu tisknutelné oblasti."

msgctxt "machine_endstop_positive_direction_x description"
msgid "Whether the endstop of the X axis is in the positive direction (high X coordinate) or negative (low X coordinate)."
msgstr "Zda koncový doraz osy X je v kladném směru (vysoká souřadnice X) nebo záporný (souřadnice nízké X)."

msgctxt "machine_endstop_positive_direction_y description"
msgid "Whether the endstop of the Y axis is in the positive direction (high Y coordinate) or negative (low Y coordinate)."
msgstr "Zda koncový doraz osy X je v kladném směru (vysoká souřadnice X) nebo záporný (souřadnice nízké X)...."

msgctxt "machine_endstop_positive_direction_z description"
msgid "Whether the endstop of the Z axis is in the positive direction (high Z coordinate) or negative (low Z coordinate)."
msgstr "Zda je koncová zarážka osy Z v kladném směru (vysoká souřadnice Z) nebo záporná (souřadnice nízké Z)."

msgctxt "machine_extruders_share_heater description"
msgid "Whether the extruders share a single heater rather than each extruder having its own heater."
msgstr "Zda extrudéry sdílejí jeden ohřívač spíše než každý extrudér mající svůj vlastní ohřívač."

msgctxt "machine_extruders_share_nozzle description"
msgid "Whether the extruders share a single nozzle rather than each extruder having its own nozzle. When set to true, it is expected that the printer-start gcode script properly sets up all extruders in an initial retraction state that is known and mutually compatible (either zero or one filament not retracted); in that case the initial retraction status is described, per extruder, by the 'machine_extruders_shared_nozzle_initial_retraction' parameter."
msgstr "Určuje, zda extrudery sdílí jednu trysku namísto, aby měl každý extruder svou vlastní trysku. Pokud je zvoleno, předpokládá se, že počáteční G kód tiskárny správně nastaví všechny extrudery do známého stavu, který je vzájemně kompatibilní (všechny filamenty jsou zatažené nebo jen jeden je nezatažený). V tomto případě je počáteční stav zatažení určen pro každý extruder parametrem 'machine_extruders_shared_nozzle_initial_retraction'."

msgctxt "machine_heated_bed description"
msgid "Whether the machine has a heated build plate present."
msgstr "Zda má stroj vyhřívanou podložku."

msgctxt "machine_heated_build_volume description"
msgid "Whether the machine is able to stabilize the build volume temperature."
msgstr "Zda je zařízení schopno stabilizovat teplotu podložky."

msgctxt "center_object description"
msgid "Whether to center the object on the middle of the build platform (0,0), instead of using the coordinate system in which the object was saved."
msgstr "Zda centrovat objekt uprostřed podložky (0,0), místo použití souřadnicového systému, ve kterém byl objekt uložen."

msgctxt "machine_nozzle_temp_enabled description"
msgid "Whether to control temperature from Cura. Turn this off to control nozzle temperature from outside of Cura."
msgstr "Zda ovládat teplotu z Cury. Vypnutím této funkce můžete regulovat teplotu trysek z vnějšku Cury."

msgctxt "material_bed_temp_prepend description"
msgid "Whether to include build plate temperature commands at the start of the gcode. When the start_gcode already contains build plate temperature commands Cura frontend will automatically disable this setting."
msgstr "Zda zahrnout příkazy pro sestavení teploty desky na začátku gcode. Pokud start_gcode již obsahuje příkazy teploty desky, Cura frontend toto nastavení automaticky deaktivuje."

msgctxt "material_print_temp_prepend description"
msgid "Whether to include nozzle temperature commands at the start of the gcode. When the start_gcode already contains nozzle temperature commands Cura frontend will automatically disable this setting."
msgstr "Zda zahrnout příkazy teploty trysek na začátku gcode. Pokud start_gcode již obsahuje příkazy teploty trysek, Cura frontend toto nastavení automaticky deaktivuje."

msgctxt "clean_between_layers description"
msgid "Whether to include nozzle wipe G-Code between layers (maximum 1 per layer). Enabling this setting could influence behavior of retract at layer change. Please use Wipe Retraction settings to control retraction at layers where the wipe script will be working."
msgstr "Zda se má G-kód stírat tryskou mezi vrstvami (maximálně 1 na vrstvu). Povolení tohoto nastavení by mohlo ovlivnit chování zatahování při změně vrstvy. Použijte nastavení retrakce čištění pro kontrolu stažení ve vrstvách, kde bude fungovat skript."

msgctxt "material_bed_temp_wait description"
msgid "Whether to insert a command to wait until the build plate temperature is reached at the start."
msgstr "Zda se má vložit příkaz k čekání, až se dosáhne teploty podložky na začátku."

msgctxt "prime_blob_enable description"
msgid "Whether to prime the filament with a blob before printing. Turning this setting on will ensure that the extruder will have material ready at the nozzle before printing. Printing Brim or Skirt can act like priming too, in which case turning this setting off saves some time."
msgstr "Zda se vlákno před tiskem připraví blobem. Zapnutím tohoto nastavení zajistíte, že extrudér bude mít před tiskem materiál připravený v trysce. Tisk límce nebo okraje může také fungovat jako základní nátěr, takže vypnutí tohoto nastavení ušetří čas."

msgctxt "print_sequence description"
msgid "Whether to print all models one layer at a time or to wait for one model to finish, before moving on to the next. One at a time mode is possible if a) only one extruder is enabled and b) all models are separated in such a way that the whole print head can move in between and all models are lower than the distance between the nozzle and the X/Y axes."
msgstr "Zda tisknout všechny modely po jedné vrstvě najednou, nebo počkat na dokončení jednoho modelu, než se přesunete k další. Jeden za časovým režimem je možný, pokud a) je povolen pouze jeden extrudér a b) všechny modely jsou odděleny tak, že celá tisková hlava se může pohybovat mezi a všechny modely jsou menší než vzdálenost mezi tryskou a X / Osy Y."

msgctxt "machine_show_variants description"
msgid "Whether to show the different variants of this machine, which are described in separate json files."
msgstr "Zda se mají zobrazit různé varianty tohoto zařízení, které jsou popsány v samostatných souborech json."

msgctxt "machine_firmware_retract description"
msgid "Whether to use firmware retract commands (G10/G11) instead of using the E property in G1 commands to retract the material."
msgstr "Zda se mají použít příkazy pro retrakci firmwaru (G10 / G11) namísto použití vlastnosti E v příkazech G1 pro stažení materiálu."

msgctxt "material_print_temp_wait description"
msgid "Whether to wait until the nozzle temperature is reached at the start."
msgstr "Zda čekat na dosažení teploty trysky na začátku."

msgctxt "infill_line_width description"
msgid "Width of a single infill line."
msgstr "Šířka jedné výplně."

msgctxt "support_interface_line_width description"
msgid "Width of a single line of support roof or floor."
msgstr "Šířka jedné řady nosných střech nebo podlah."

msgctxt "roofing_line_width description"
msgid "Width of a single line of the areas at the top of the print."
msgstr "Šířka jedné řady oblastí v horní části tisku."

msgctxt "line_width description"
msgid "Width of a single line. Generally, the width of each line should correspond to the width of the nozzle. However, slightly reducing this value could produce better prints."
msgstr "Šířka jedné řádky. Obecně by šířka každé linie měla odpovídat šířce trysky. Avšak mírné snížení této hodnoty by mohlo vést k lepším výtiskům."

msgctxt "prime_tower_line_width description"
msgid "Width of a single prime tower line."
msgstr "Šířka jedné hlavní věže."

msgctxt "skirt_brim_line_width description"
msgid "Width of a single skirt or brim line."
msgstr "Šířka čáry okraje nebo límce."

msgctxt "support_bottom_line_width description"
msgid "Width of a single support floor line."
msgstr "Šířka jedné podpůrné podlahové linie."

msgctxt "support_roof_line_width description"
msgid "Width of a single support roof line."
msgstr "Šířka jedné podpůrné linie střechy."

msgctxt "support_line_width description"
msgid "Width of a single support structure line."
msgstr "Šířka jedné linie podpůrné struktury."

msgctxt "skin_line_width description"
msgid "Width of a single top/bottom line."
msgstr "Šířka jedné horní/spodní čáry."

msgctxt "wall_line_width_x description"
msgid "Width of a single wall line for all wall lines except the outermost one."
msgstr "Šířka jedné linie stěny pro všechny linie stěny kromě té nejvíce venkovní."

msgctxt "wall_line_width description"
msgid "Width of a single wall line."
msgstr "Šířka jedné stěny."

msgctxt "raft_base_line_width description"
msgid "Width of the lines in the base raft layer. These should be thick lines to assist in build plate adhesion."
msgstr "Šířka čar v základní vrstvě raftu. Měly by to být silné čáry, které napomáhají při přilnutí desky."

msgctxt "raft_interface_line_width description"
msgid "Width of the lines in the middle raft layer. Making the second layer extrude more causes the lines to stick to the build plate."
msgstr "Šířka čar ve střední vrstvě raftu. Další vytlačování druhé vrstvy způsobí, že se linie přilepí na podložku."

msgctxt "raft_surface_line_width description"
msgid "Width of the lines in the top surface of the raft. These can be thin lines so that the top of the raft becomes smooth."
msgstr "Šířka čar v horním povrchu raftu. Mohou to být tenké čáry, takže horní část raftu je hladká."

msgctxt "wall_line_width_0 description"
msgid "Width of the outermost wall line. By lowering this value, higher levels of detail can be printed."
msgstr "Šířka čáry nejkrajnější stěny. Snížením této hodnoty lze vytisknout vyšší úrovně detailů."

msgctxt "min_bead_width description"
msgid "Width of the wall that will replace thin features (according to the Minimum Feature Size) of the model. If the Minimum Wall Line Width is thinner than the thickness of the feature, the wall will become as thick as the feature itself."
msgstr "Šířka zdi, která nahradí tenké části modelu (v souladu s nastavením Minimální velikost částí). Pokud je Minimální šířka tenkých stěn nižší, než část určitá modelu, bude zeď tištěna skutečnou tloušťkou této části."

msgctxt "wipe_brush_pos_x label"
msgid "Wipe Brush X Position"
msgstr "Pozice X stěrače"

msgctxt "wipe_hop_speed label"
msgid "Wipe Hop Speed"
msgstr "Rychlost čištění Hopu"

msgctxt "prime_tower_wipe_enabled label"
msgid "Wipe Inactive Nozzle on Prime Tower"
msgstr "Otřete neaktivní trysku na Prime Tower"

msgctxt "wipe_move_distance label"
msgid "Wipe Move Distance"
msgstr "Velikost pohybu při čištění"

msgctxt "clean_between_layers label"
msgid "Wipe Nozzle Between Layers"
msgstr "Čistit trysku mezi vrstvami"

msgctxt "wipe_pause label"
msgid "Wipe Pause"
msgstr "Pozastavit čištění"

msgctxt "wipe_repeat_count label"
msgid "Wipe Repeat Count"
msgstr "Počet opakování čištění"

msgctxt "wipe_retraction_amount label"
msgid "Wipe Retraction Distance"
msgstr "Vzdálenost retrakce při čištění"

msgctxt "wipe_retraction_enable label"
msgid "Wipe Retraction Enable"
msgstr "Povolit retrakci při čištění"

msgctxt "wipe_retraction_extra_prime_amount label"
msgid "Wipe Retraction Extra Prime Amount"
msgstr "Množství zasunutí filamentu při prvotním čištění"

msgctxt "wipe_retraction_prime_speed label"
msgid "Wipe Retraction Prime Speed"
msgstr "Primární rychlost retrakce při čištění"

msgctxt "wipe_retraction_retract_speed label"
msgid "Wipe Retraction Retract Speed"
msgstr "Rychlost navíjení stírání"

msgctxt "wipe_retraction_speed label"
msgid "Wipe Retraction Speed"
msgstr "Rychlost retrakce při čištění"

msgctxt "wipe_hop_enable label"
msgid "Wipe Z Hop"
msgstr "Čistit Z Hop"

msgctxt "wipe_hop_amount label"
msgid "Wipe Z Hop Height"
msgstr "Výška čištění Z Hopu"

msgctxt "retraction_combing option infill"
msgid "Within Infill"
msgstr "V rámci výplně"

msgctxt "machine_always_write_active_tool description"
msgid "Write active tool after sending temp commands to inactive tool. Required for Dual Extruder printing with Smoothie or other firmware with modal tool commands."
msgstr "Zapisování aktivního nástroje po odeslání dočasných příkazů neaktivnímu nástroji. Vyžadováno pro tisk s dvojitým extruderem se Smoothie, či jiným firmwarem s modálními příkazy nástrojů."

msgctxt "machine_endstop_positive_direction_x label"
msgid "X Endstop in Positive Direction"
msgstr "Endstop X v kladném směru"

msgctxt "wipe_brush_pos_x description"
msgid "X location where wipe script will start."
msgstr "X místo, kde bude spuštěn čistící skript."

msgctxt "support_xy_overrides_z option xy_overrides_z"
msgid "X/Y overrides Z"
msgstr "X/Y přepisuje Z"

msgctxt "machine_endstop_positive_direction_y label"
msgid "Y Endstop in Positive Direction"
msgstr "Endstop Y v kladném směru"

msgctxt "machine_endstop_positive_direction_z label"
msgid "Z Endstop in Positive Direction"
msgstr "Endstop Z v kladném směru"

msgctxt "retraction_hop_after_extruder_switch label"
msgid "Z Hop After Extruder Switch"
msgstr "Z Hop po přepnutí extruderu"

msgctxt "retraction_hop_after_extruder_switch_height label"
msgid "Z Hop After Extruder Switch Height"
msgstr "Výška Z Hopu po přepnutí extruderu"

msgctxt "retraction_hop label"
msgid "Z Hop Height"
msgstr "Výška Z Hopu"

msgctxt "retraction_hop_only_when_collides label"
msgid "Z Hop Only Over Printed Parts"
msgstr "Z Hop pouze přes tištěné díly"

msgctxt "speed_z_hop label"
msgid "Z Hop Speed"
msgstr "Rychlost Z Hopu"

msgctxt "retraction_hop_enabled label"
msgid "Z Hop When Retracted"
msgstr "Z Hop po zatažení"

msgctxt "z_seam_type label"
msgid "Z Seam Alignment"
msgstr "Vyrovnávní spojů na ose Z"

msgctxt "z_seam_on_vertex label"
msgid "Z Seam On Vertex"
msgstr ""

msgctxt "z_seam_position label"
msgid "Z Seam Position"
msgstr "Z pozice švu"

msgctxt "z_seam_relative label"
msgid "Z Seam Relative"
msgstr "Relativní Z šev"

msgctxt "z_seam_x label"
msgid "Z Seam X"
msgstr "Z šev X"

msgctxt "z_seam_y label"
msgid "Z Seam Y"
msgstr "Z šev Y"

msgctxt "support_xy_overrides_z option z_overrides_xy"
msgid "Z overrides X/Y"
msgstr "Z přepisuje X/Y"

msgctxt "infill_pattern option zigzag"
msgid "Zig Zag"
msgstr "Cik-cak"

msgctxt "ironing_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "roofing_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "support_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "support_interface_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "support_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "support_roof_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "top_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "top_bottom_pattern_0 option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "travel description"
msgid "travel"
msgstr "cestování"

#~ msgctxt "brim_inside_margin description"
#~ msgid "A part fully enclosed inside another part can generate an outer brim that touches the inside of the other part. This removes all brim within this distance from internal holes."
#~ msgstr "Část plně obklopená jinou částí může generovat vnější límec, který se dotýká vnitřku obklopující části. Toto nastavení odstraní límec v dané vzdálenosti od vnitřních otvorů."

#~ msgctxt "user_defined_print_order_enabled description"
#~ msgid "Allows to order the object list to set the print sequence manually. First object from the list will be printed first."
#~ msgstr "Umožňuje řadit seznam objektů pro ruční nastavení tiskové sekvence. První objekt ze seznamu bude vytisknut jako první."

#~ msgctxt "brim_inside_margin label"
#~ msgid "Brim Inside Avoid Margin"
#~ msgstr "Vzdálenost od límce uvnitř"

#~ msgctxt "brim_outside_only label"
#~ msgid "Brim Only on Outside"
#~ msgstr "Límec pouze venku"

#~ msgctxt "layer_0_z_overlap description"
#~ msgid "Make the first and second layer of the model overlap in the Z direction to compensate for the filament lost in the airgap. All models above the first model layer will be shifted down by this amount."
#~ msgstr "První a druhá vrstva modelu se překrývají ve směru Z, aby se kompenzovalo vlákno ztracené ve vzduchové mezeře. Všechny modely nad první vrstvou modelu budou o tuto částku posunuty dolů."

#~ msgctxt "machine_nozzle_head_distance label"
#~ msgid "Nozzle Length"
#~ msgstr "Délka trysky"

#~ msgctxt "brim_outside_only description"
#~ msgid "Only print the brim on the outside of the model. This reduces the amount of brim you need to remove afterwards, while it doesn't reduce the bed adhesion that much."
#~ msgstr "Límec tiskněte pouze na vnější stranu modelu. Tím se snižuje množství límce, který je třeba následně odstranit, zatímco to tolik nesnižuje přilnavost k podložce."

#~ msgctxt "support_interface_skip_height label"
#~ msgid "Support Interface Resolution"
#~ msgstr "Rozlišení rozhraní podpor"

#~ msgctxt "machine_nozzle_head_distance description"
#~ msgid "The height difference between the tip of the nozzle and the lowest part of the print head."
#~ msgstr "Výškový rozdíl mezi špičkou trysky a nejnižší částí tiskové hlavy."

#~ msgctxt "wall_overhang_angle description"
#~ msgid "Walls that overhang more than this angle will be printed using overhanging wall settings. When the value is 90, no walls will be treated as overhanging. Overhang that gets supported by support will not be treated as overhang either."
#~ msgstr "Stěny, které přesahují více než tento úhel, budou vytištěny pomocí nastavení převislých stěn. Pokud je hodnota 90, nebudou se žádné stěny považovat za převislé. Převis, který je podporován podporou, nebude považován ani za převis."

#~ msgctxt "support_interface_skip_height description"
#~ msgid "When checking where there's model above and below the support, take steps of the given height. Lower values will slice slower, while higher values may cause normal support to be printed in some places where there should have been support interface."
#~ msgstr "Při kontrole, kde je model nad a pod podpěrou, proveďte kroky dané výšky. Nižší hodnoty se budou řezat pomaleji, zatímco vyšší hodnoty mohou způsobit tisk normální podpory na místech, kde mělo být rozhraní podpory."
